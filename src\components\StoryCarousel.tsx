import React from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { IMAGES } from '../constant/image';

const STORY_SLIDES = [
  {
    title: "Your journey starts here. Play now to start earning FREE airtime & crypto",
    image: IMAGES.BANNER_1,
  },
  {
    title: "Play free. Earn real.",
    image: IMAGES.BANNER_2,
  },
  {
    title: "Work together. Earn more.",
    image: IMAGES.BANNER_3,
  },
  {
    title: "Enter daily. Win big.",
    image: IMAGES.BANNER_4
  },
];

const StoryCarousel = () => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });

  const scrollPrev = React.useCallback(() => emblaApi && emblaApi.scrollPrev(), [emblaApi]);
  const scrollNext = React.useCallback(() => emblaApi && emblaApi.scrollNext(), [emblaApi]);

  return (
    <div className="relative mb-8 group ">
      <div className="overflow-hidden rounded-2xl" ref={emblaRef}>
        <div className="flex">
          {STORY_SLIDES.map((slide, idx) => (
            <div key={idx} className="relative flex-[0_0_100%] min-w-0">
              {/* Image Container */}
              <div className="relative w-full h-[195px] rounded-2xl overflow-hidden mb-2">
                <img
                  src={slide.image}
                  alt={slide.title}
                  className={`inset-0 w-full h-full ${idx !== 0 && 'object-cover'
                    } md:object-fill`}
                />
                {/* Dark overlay */}
                {idx !== 0 &&

                  <div className="absolute inset-0 bg-black bg-opacity-35" />
                }

                {/* First slide: show text at bottom inside image */}
                {idx === 0 && (
                  <>
                    {/* Logo top-center or top-left */}
                    <div className="absolute inset-0 top-8  flex  justify-center">
                      <img
                        src={IMAGES.LOGO}
                        alt="Logo"
                        className="w-[155px] h-[5rem] md:h-[5rem] sm:h-[4rem] drop-shadow-lg"
                      />
                    </div>

                    {/* Text bottom-center */}
                    <div className="absolute bottom-6 left-0 right-0 px- sm:px-6 text-center">
                      <p className="text-white text-[12px] sm:text-base leading-snug drop-shadow-md">
                        Your journey starts here. Play now to start<br />
                        earning FREE airtime &amp; crypto.
                      </p>
                    </div>
                  </>
                )}

                {/* Other slides: center text inside image */}
                {idx !== 0 && (
                  <div className="absolute inset-0 flex items-center justify-left p-8 md:p-[26px]">
                    <p className="text-center text-white font-bold text-xl sm:text-2xl tracking-wide">
                      {slide.title
                        .split('.')
                        .filter(line => line.trim() !== '')
                        .map((line, lineIdx) => (
                          <span
                            key={lineIdx}
                            className={`block ${lineIdx === 0 ? 'text-white' : 'text-yellow-300'
                              }`}
                          >
                            {line.trim()}
                            {line.trim().endsWith('.') ? '' : '.'}
                          </span>
                        ))}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Buttons */}
      <button
        onClick={scrollPrev}
        className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-black/50 flex items-center justify-center text-white group-hover:opacity-100 transition-opacity"
      >
        <ChevronLeft size={24} />
      </button>
      <button
        onClick={scrollNext}
        className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-black/50 flex items-center justify-center text-white group-hover:opacity-100 transition-opacity"
      >
        <ChevronRight size={24} />
      </button>
    </div>
  );
};

export default StoryCarousel;
