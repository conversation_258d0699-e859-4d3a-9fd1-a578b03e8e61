export interface RewardAd {
  type: string;
  url: string;
}

export interface WebAdPayload {
  configRefreshInterval: number;
  rewardAds: RewardAd[];
}

class WebAdService {
  private readonly apiUrl: string = import.meta.env?.VITE_AD_BASE_URL + "/getWebAds";
  private readonly apiKey: string = import.meta.env?.VITE_AD_API_KEY || '';
  private readonly localStorageKey: string = 'webAdsPayload';
  private readonly maxRetries: number = 3;
  private readonly retryDelay: number = 3000; 

  private payload: WebAdPayload | null = null;
  private currentIndex: number = 0;
  private refreshTimer: NodeJS.Timeout | null = null;
  
  constructor() {
    this.init();
  }

  private async init(): Promise<void> {
    this.loadFromLocalStorage();
    
    await this.fetchAdsWithRetry();
    this.scheduleNextRefresh();
  }

  private loadFromLocalStorage(): void {
    try {
      const storedPayload = localStorage.getItem(this.localStorageKey);
      if (storedPayload) {
        this.payload = JSON.parse(storedPayload) as WebAdPayload;
        console.log('Loaded payload from localStorage:', this.payload);
      }
    } catch (error) {
      console.error('Failed to load from localStorage:', error);
    }
  }

  private saveToLocalStorage(payload: WebAdPayload): void {
    try {
      localStorage.setItem(this.localStorageKey, JSON.stringify(payload));
      console.log('Payload saved to localStorage');
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  }

  private async fetchAdsWithRetry(retryCount: number = 0): Promise<WebAdPayload> {
    try {
      const response = await fetch(this.apiUrl, {
        method: 'GET',
        headers: {
          'x-api-key': this.apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: WebAdPayload = await response.json();
      
      this.payload = data;
      this.saveToLocalStorage(data);
      
      console.log('Web ads fetched successfully:', data);
      
      return data;
    } catch (error) {
      console.error(`Fetch attempt ${retryCount + 1} failed:`, error);
      
      if (retryCount < this.maxRetries - 1) {
        console.log(`Retrying in ${this.retryDelay}ms...`);
        await this.delay(this.retryDelay);
        return this.fetchAdsWithRetry(retryCount + 1);
      } else {
        console.error('All retry attempts failed');
        throw error;
      }
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private scheduleNextRefresh(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    if (this.payload && this.payload.configRefreshInterval) {
      const refreshInterval = this.payload.configRefreshInterval * 1000; 
      
      this.refreshTimer = setTimeout(async () => {
        console.log('Refreshing web ads...');
        try {
          await this.fetchAdsWithRetry();
          this.scheduleNextRefresh();
        } catch (error) {
          console.error('Failed to refresh ads:', error);
          this.scheduleNextRefresh();
        }
      }, refreshInterval);
      
      console.log(`Next refresh scheduled in ${this.payload.configRefreshInterval} seconds`);
    }
  }

  public getFullPayload(): WebAdPayload | null {
    return this.payload;
  }

  public getNextAd(): RewardAd | null {
    if (!this.payload || !this.payload.rewardAds || this.payload.rewardAds.length === 0) {
      console.warn('No reward ads available');
      return null;
    }

    const rewardAds = this.payload.rewardAds;
    
    if (this.currentIndex >= rewardAds.length) {
      this.currentIndex = 0;
    }

    const nextAd = rewardAds[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % rewardAds.length;
    
    console.log(`Returning ad at index ${this.currentIndex - 1 < 0 ? rewardAds.length - 1 : this.currentIndex - 1}:`, nextAd);
    
    return nextAd;
  }

  public destroy(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  public async refreshAds(): Promise<boolean> {
    try {
      await this.fetchAdsWithRetry();
      this.scheduleNextRefresh();
      return true;
    } catch (error) {
      console.error('Manual refresh failed:', error);
      return false;
    }
  }

  public getCurrentIndex(): number {
    return this.currentIndex;
  }

  public resetIndex(): void {
    this.currentIndex = 0;
  }
}

const webAdService = new WebAdService();

export default webAdService;