
declare global {
  interface Window {
    nativeHandler?: {
      postMessage: (message: string) => void;
    };
    onNativeMessage?: (payload: NativeMessagePayload) => void;
  }
}

export interface NativeMessagePayload<T = unknown> {
  action: string;
  data?: T;
}

type RewardDetails = {
  userId: string;
};

class NativeAdService {
  static showRewardedAd(rewardDetails: RewardDetails) {
    console.log("Native rewardedAd triggered");
    const message: NativeMessagePayload<RewardDetails> = {
      action: "watchRewardedAd",
      data: rewardDetails,
    };
    window.nativeHandler?.postMessage(JSON.stringify(message));
  }

  static showInterstitialAd() {
    console.log("Native interstitualAd triggered");
    const message: NativeMessagePayload<RewardDetails> = {
      action: "watchInterstitialAd",
    };
    window.nativeHandler?.postMessage(JSON.stringify(message));
  }

  static listen(callback: (action: string, data?: unknown) => void) {
    window.onNativeMessage = (payload: NativeMessagePayload) => {
      try {
        const parsed: NativeMessagePayload = typeof payload === 'string'
          ? JSON.parse(payload)
          : payload;

        callback(parsed.action, parsed.data);
      } catch (error) {
        console.error("Error parsing native message:", error);
      }
    };
  }

  static cleanup() {
    window.onNativeMessage = undefined;
  }
}

export default NativeAdService;
