<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/x-icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Rise & Hustle</title>
  
  <!-- Critical CSS -->
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: system-ui, -apple-system, sans-serif; 
      background: #1a1a1a; 
      color: white;
      overflow-x: hidden;
    }
    #root { min-height: 100vh; }
    
    /* Loading spinner */
    .loading-spinner {
      width: 40px; height: 40px;
      border: 4px solid #333;
      border-top: 4px solid #646cff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }
    @keyframes spin { to { transform: rotate(360deg); } }
    
    /* Hide heavy content until loaded */
    .heavy-content { opacity: 0; transition: opacity 0.3s; }
    .loaded .heavy-content { opacity: 1; }
  </style>
  
  <!-- Preload critical fonts -->
  <link rel="preload" href="/src/assets/font/Haydes.ttf" as="font" type="font/ttf" crossorigin>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>
