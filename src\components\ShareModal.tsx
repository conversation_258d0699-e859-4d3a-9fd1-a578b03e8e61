import React, { useState, useEffect } from 'react';
import { X, Zap, Facebook, Twitter, Instagram } from 'lucide-react';
import { postRewardClaim } from '../api/rewardsService';
import { useAuth } from '../auth/AuthContext';
import { CustomToast } from '../utils/validations/customeToast';
import { RewardGameType } from '../types/Rewards';
import { useCoins } from '../hooks/useCoinsQuery'

/**
 * Props for the ShareModal component
 * @interface ShareModalProps
 * @property {function} onClose - Function to close the modal
 * @property {function} onWatchAd - Function to handle watch ad action
 * @property {function} onShare - Function to handle share action with platform type
 */
interface ShareModalProps {
  onClose: () => void;
}

/**
 * Available boost options with rewards
 * @constant {Array<BoostOption>}
 */
const BOOST_OPTIONS = [
  {
    id: RewardGameType.FACEBOOK_SHARE,
    title: 'Share on Facebook',
    description: 'Share your progress with friends',
    icon: Facebook,
    xp: 100,
  },
  {
    id: RewardGameType.TWITTER_SHARE,
    title: 'Share on Twitter',
    description: 'Tweet about your achievements',
    icon: Twitter,
    xp: 100,
  },
  {
    id: RewardGameType.INSTAGRAM_SHARE,
    title: 'Share on Instagram',
    description: 'Post your game highlights',
    icon: Instagram,
    xp: 100,
  },
];

/**
 * Modal component that displays various ways to boost progress
 * @component
 * @param {ShareModalProps} props - Component props
 * @returns {React.ReactElement} Rendered component
 */
const ShareModal: React.FC<ShareModalProps> = ({ onClose, }) => {
  // Hooks and state
  const { userProfile } = useAuth();
  const { refetchCoins } = useCoins();
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    const checkMobile = () => window.innerWidth < 768;
    setIsMobile(checkMobile());
    
    const handleResize = () => setIsMobile(checkMobile());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const [isMobile, setIsMobile] = useState(false);


  // Generate share URL from user's referral code
  const referralCode = userProfile?.referral_code;

  async function copyToClipboard(text: string): Promise<boolean> {
    if (navigator.clipboard?.writeText) {
      try {
        await navigator.clipboard.writeText(text);
        return true;
      } catch {
        // fall back
      }
    }
    // fallback
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.setAttribute('readonly', '');
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    document.body.appendChild(textarea);
    textarea.select();
    let ok = false;
    try {
      ok = document.execCommand('copy');
    } catch (err) {console.error(err)}
      document.body.removeChild(textarea);
    return ok;
  }

  /**
   * Handle sharing to social platforms and claim rewards
   * @param {RewardGameType} platform - Platform to share to
   * @returns {Promise<void>}
   */
const handleShareAndClaim = async (platform: RewardGameType) => {
  const shareUrl = window.location.href;
  const message = `I just leveled up on Rise and Hustle!`;

  let url = '';
  switch (platform) {
    case RewardGameType.FACEBOOK_SHARE:
      url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(message)}`;
      break;
    case RewardGameType.TWITTER_SHARE:
      url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message + ' ' + shareUrl)}`;
      break;
    case RewardGameType.INSTAGRAM_SHARE:
      await copyToClipboard(shareUrl);
      CustomToast('success', 'Link copied! Paste it in your Instagram post.');
      return;
    default:
      return;
  }
  
  if (url) {
    window.open(url, '_blank', 'noopener,noreferrer');
    await copyToClipboard(shareUrl);
    CustomToast('success', 'Link copied to clipboard!');
  }
};

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-b from-[#510957] to-black rounded-2xl w-full max-w-lg text-white">
        <div className="p-6 flex justify-between items-center sticky top-0 rounded-t">
          <h2 className="text-xl font-bold">Share Now</h2>
          <button onClick={onClose} className="text-white/60 hover:text-white">
            <X size={24} />
          </button>
        </div>
        <div className="p-6 space-y-4">
          {BOOST_OPTIONS.map((option) => {
            const Icon = option.icon;
            return (
              <button
                key={option.id}
                onClick={async () => {
                  await handleShareAndClaim(option.id);
                  onClose();
                }}
                className="w-full bg-[#131313] p-4 rounded-xl flex items-center gap-4"
              >
                <div className="bg-white/10 p-3 rounded-lg">
                  <Icon size={24} className="text-[#ED0CFF]" />
                </div>
                <div className="flex-1 text-left">
                  <h3 className="font-medium">{option.title}</h3>
                  <p className="text-sm text-white/60">{option.description}</p>
                </div>
                <div className="flex items-center gap-1 text-white">
                  <span>+{option.xp}</span>
                  <span className="uppercase">Bucks</span>
                </div>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

/**
 * Export the ShareModal component
 */
export default ShareModal;
