import axios from './axiosInstance'
import { RewardGameType } from '../types/Rewards';


export interface RewardClaimPayload {
  user_id: string;
  game: RewardGameType;
  position?: number;
  transaction_id?: string;
  wallet_id?: string;
}

export interface RewardClaimResponse {
  success: boolean;
  message: string;

}

const API_BASE_URL = import.meta.env?.VITE_REWARD_BASE_URL

export const postRewardClaim = async (
  payload: RewardClaimPayload
): Promise<RewardClaimResponse> => {
  try {
    const finalPayload = {
      ...payload,
      source: 'web_app',
    };

    console.log("Claiming reward", JSON.stringify(finalPayload));
    const response = await axios.post<RewardClaimResponse>(
      `${API_BASE_URL}/reward-claim`,
      finalPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': import.meta.env?.VITE_REWARD_API_KEY || '',
        },
      }
    );

    if (response?.data?.success) {
      console.log("Reward claimed successfully");
    }

    return response.data;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error('Failed to post reward claim:', error.response?.data || error.message);
    throw error;
  }
};


