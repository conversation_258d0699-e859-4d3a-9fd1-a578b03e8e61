import React, { useState, useEffect } from 'react';
import { X, Mail, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '../auth/AuthContext';
import { updateUserPhone } from '../api/authService';

interface ChangeEmailProps {
  onClose: () => void;
}

const ChangeEmail: React.FC<ChangeEmailProps> = ({ onClose }) => {
  const [step, setStep] = useState<'current' | 'new' | 'success'>('current');
  const { userProfile } = useAuth();
  const [newEmail, setNewEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // const handleCodeChange = (index: number, value: string) => {
  //   if (value.length <= 1) {
  //     const newCode = [...verificationCode];
  //     newCode[index] = value;
  //     setVerificationCode(newCode);

  //     // Auto-focus next input
  //     if (value && index < 5) {
  //       const nextInput = document.querySelector(`input[name="code-${index + 1}"]`) as HTMLInputElement;
  //       if (nextInput) nextInput.focus();
  //     }
  //   }
  // };

  // const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
  //   if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
  //     const prevInput = document.querySelector(`input[name="code-${index - 1}"]`) as HTMLInputElement;
  //     if (prevInput) prevInput.focus();
  //   }
  // };

  const handleCurrentStep = (e: React.FormEvent) => {
    e.preventDefault();
    if (password.length < 8) {
      setError('Please enter your current password');
      return;
    }
    setError(null);
    setStep('new');
  };

  const handleNewEmailStep = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newEmail || !/^[0-9]{10}$/.test(newEmail)) {
      setError('Please enter a valid phone number');
      return;
    }
    if (newEmail === userProfile?.phone_number) {
      setError('New phone number must be different from current phone number');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    const phoneNumber=`+234${newEmail}`
    try {
      await updateUserPhone({
        password: password,
        new_phone: phoneNumber,
      });
      setStep('success');
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to update phone number');
    } finally {
      setIsLoading(false);
    }
  };

  // const handleVerification = (e: React.FormEvent) => {
  //   e.preventDefault();
  //   const code = verificationCode.join('');
  //   if (code.length !== 6) {
  //     setError('Please enter the complete verification code');
  //     return;
  //   }
  //   setError(null);
  //   setStep('success');
  // };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl w-full max-w-lg flex flex-col">
        <div className="p-6 border-b border-white/10 flex justify-between items-center bg-gradient-to-r from-[#510957] to-[#510957] rounded-t-2xl">
          <h2 className="text-xl text-white font-[Anton] tracking-wide">Change Phone Number</h2>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6 text-white">
          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-8">
            {['current', 'new'].map((s, index) => (
              <React.Fragment key={s}>
                <div className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    step === s 
                      ? 'bg-[#ED0CFF] text-white' 
                      : step === 'success' || ['current', 'new'].indexOf(step) > ['current', 'new'].indexOf(s)
                      ? 'bg-green-500 text-white'
                      : 'bg-white/10 text-white/60'
                  }`}>
                    {step === 'success' || ['current', 'new'].indexOf(step) > ['current', 'new'].indexOf(s)
                      ? <CheckCircle size={16} />
                      : index + 1
                    }
                  </div>
                  <span className="ml-2 text-sm font-medium capitalize">{s}</span>
                </div>
                {index < 1 && (
                  <div className="flex-1 mx-4 h-0.5 bg-white/10" />
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Current Phone Step */}
          {step === 'current' && (
            <form onSubmit={handleCurrentStep} className="space-y-6">
              <div className="bg-white/5 rounded-xl p-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="bg-blue-500/20 p-2 rounded-lg">
                    <Mail size={20} className="text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-white/60">Current Phone Number</p>
                    <p className="font-medium">{userProfile?.phone_number || 'Not available'}</p>
                  </div>
                </div>
              </div>
            
              <div>
                <label className="block text-sm font-medium mb-2">
                  Confirm Password
                </label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-yellow-500"
                  placeholder="Enter your password"
                />
              </div>

              {error && (
                <div className="flex items-center gap-2 text-red-400 text-sm">
                  <AlertCircle size={16} />
                  <span>{error}</span>
                </div>
              )}

              <button
                type="submit"
                className="w-full py-3 bg-[#ED0CFF] text-white rounded-lg font-medium hover:bg-[#d30ae0] transition-colors"
              >
                Continue
              </button>
            </form>
          )}

          {/* New Phone Number Step */}
          {step === 'new' && (
            <form onSubmit={handleNewEmailStep} className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">
                  New Phone Number
                </label>
                <input
                  type="tel"
                  value={newEmail}
                  onChange={(e) => setNewEmail(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
                  placeholder="Enter new phone number"
                />
              </div>

              {error && (
                <div className="flex items-center gap-2 text-red-400 text-sm">
                  <AlertCircle size={16} />
                  <span>{error}</span>
                </div>
              )}

              <div className="flex gap-4">
                <button
                  type="button"
                  onClick={() => setStep('current')}
                  className="flex-1 py-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                >
                  Back
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="flex-1 py-3 bg-[#ED0CFF] text-white rounded-lg font-medium hover:bg-[#d30ae0] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Updating...' : 'Update Phone Number'}
                </button>
              </div>
            </form>
          )}



          {/* Success Step */}
          {step === 'success' && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle size={32} className="text-green-400" />
              </div>
              <h3 className="text-xl font-bold mb-2">Phone Number Changed Successfully!</h3>
              <p className="text-white/60 mb-6">
                Your phone number has been updated to<br />
                <span className="text-white font-medium">{newEmail}</span>
              </p>
              <button
                onClick={onClose}
                className="w-full py-3 bg-[#ED0CFF] text-white rounded-lg font-medium hover:bg-[#d30ae0] transition-colors"
              >
                Done
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChangeEmail;