/* Critical above-the-fold styles */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #646cff;
  --bg-color: #1a1a1a;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', system-ui, -apple-system, sans-serif;
  background-color: var(--bg-color);
  color: white;
  line-height: 1.5;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Defer non-critical styles */
@media (min-width: 768px) {
  /* Desktop specific styles */
}

/* Load heavy animations only after page load */
.loaded .heavy-animation {
  animation: slideIn 0.3s ease-out;
}
