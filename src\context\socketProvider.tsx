import { createContext, useContext, useEffect, useState } from 'react';
import {
  SocketURL,
  SOCKET_URL_FOR_SQUAD_LEVEL,
  SOCKET_URL_FOR_SQUAD_PROGRESS_LEVEL,
  SOCKET_URL_FOR_NOTIFY,
  SOCKET_URL_FOR_BALANCE,
} from '../api/auth';
import useWebSocket from '../hooks/useSocket';

type SocketContextType = {
  socket: WebSocket | null;
  levelPlayerSocket: WebSocket | null;
  squadLevelSocket: WebSocket | null;
  notifySocket: WebSocket | null;
  balanceSocket: WebSocket | null;

  socketState: any;
  levelPlayerState: any;
  squadLevelState: any;
  notifyState: any;
  balanceState: any;
  setSocketState: (data: any) => void;
  setLevelPlayerState: (data: any) => void;
  setSquadLevelState: (data: any) => void;
  setNotifyState: (data: any) => void;
  setBalanceState: (data: any) => void;

  connectSocket: () => void;
  connectLevelPlayerSocket: () => void;
  connectSquadLevelSocket: () => void;
  connectNotifySocket: () => void;
  connectBalanceSocket: () => void;

  disconnectSocket: () => void;
  disconnectLevelPlayerSocket: () => void;
  disconnectSquadLevelSocket: () => void;
  disconnectNotifySocket: () => void;
  disconnectBalanceSocket: () => void;

  isConnected: boolean;
  isLevelPlayerConnected: boolean;
  isSquadLevelConnected: boolean;
  isNotifyConnected: boolean;
  isBalanceConnected: boolean;
};
const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider = ({ children }: { children: React.ReactNode }) => {
  // Initialize with connectOnMount: false to prevent auto-connect at start
  const { socket, isConnected, connect, disconnect } = useWebSocket(SocketURL, {
    connectOnMount: false,
    autoReconnect: false,
  });

  const {
    socket: levelPlayerSocket,
    isConnected: isLevelPlayerConnected,
    connect: connectLevelPlayerSocket,
    disconnect: disconnectLevelPlayerSocket,
  } = useWebSocket(SOCKET_URL_FOR_SQUAD_LEVEL, { connectOnMount: false });

  const {
    socket: squadLevelSocket,
    isConnected: isSquadLevelConnected,
    connect: connectSquadLevelSocket,
    disconnect: disconnectSquadLevelSocket,
  } = useWebSocket(SOCKET_URL_FOR_SQUAD_PROGRESS_LEVEL, {
    connectOnMount: false,
  });

  const {
    socket: notifySocket,
    isConnected: isNotifyConnected,
    connect: connectNotifySocket,
    disconnect: disconnectNotifySocket,
  } = useWebSocket(SOCKET_URL_FOR_NOTIFY, { connectOnMount: false });

  const {
    socket: balanceSocket,
    isConnected: isBalanceConnected,
    connect: connectBalanceSocket,
    disconnect: disconnectBalanceSocket,
  } = useWebSocket(SOCKET_URL_FOR_BALANCE, { connectOnMount: false });

  const [socketState, setSocketState] = useState<any>(null);
  const [levelPlayerState, setLevelPlayerState] = useState<any>(null);
  const [squadLevelState, setSquadLevelState] = useState<any>(null);
  const [notifyState, setNotifyState] = useState<any>(null);
  const [balanceState, setBalanceState] = useState<any>(null);

  useEffect(() => {
    if (!socket) return;

    const messageHandler = (event: MessageEvent) => {
      try {
        const parsed = event.data ? JSON.parse(event.data) : null;
        setSocketState(parsed);
      } catch (error) {
        console.error('Failed to parse socket message:', error);
      }
    };

    socket.addEventListener('message', messageHandler);

    return () => {
      socket.removeEventListener('message', messageHandler);
    };
  }, [socket]);

  useEffect(() => {
    if (!levelPlayerSocket) return;

    const handler = (event: MessageEvent) => {
      try {
        const message = event.data;
        if (
          typeof message === 'string' &&
          (message.startsWith('{') || message.startsWith('['))
        ) {
          const parsed = JSON.parse(message);
          setLevelPlayerState(parsed);
        } else {
          console.warn('Non-JSON message received:', message);
        }
      } catch (err) {
        console.error('Error parsing level player socket data:', err);
      }
    };

    levelPlayerSocket.addEventListener('message', handler);
    return () => levelPlayerSocket.removeEventListener('message', handler);
  }, [levelPlayerSocket]);

  useEffect(() => {
    if (!squadLevelSocket) return;

    const handler = (event: MessageEvent) => {
      try {
        const message = event.data;
        if (
          typeof message === 'string' &&
          (message.startsWith('{') || message.startsWith('['))
        ) {
          const parsed = JSON.parse(message);
          setSquadLevelState(parsed);
        } else {
          console.warn('Non-JSON message received:', message);
        }
      } catch (err) {
        console.error('Error parsing squad level socket data:', err);
      }
    };

    squadLevelSocket.addEventListener('message', handler);
    return () => {
      squadLevelSocket.removeEventListener('message', handler);
    };
  }, [squadLevelSocket]);

  useEffect(() => {
    if (!notifySocket) return;

    const handler = (event: MessageEvent) => {
      try {
        const message = event.data;
        if (
          typeof message === 'string' &&
          (message.startsWith('{') || message.startsWith('['))
        ) {
          const parsed = JSON.parse(message);
          setNotifyState(parsed);
        } else {
          console.warn('Non-JSON message received:', message);
        }
      } catch (err) {
        console.error('Error parsing notify socket data:', err);
      }
    };

    notifySocket.addEventListener('message', handler);
    return () => {
      notifySocket.removeEventListener('message', handler);
    };
  }, [notifySocket]);

  useEffect(() => {
    if (!balanceSocket) return;

    const handler = (event: MessageEvent) => {
      try {
        const message = event.data;
        if (
          typeof message === 'string' &&
          (message.startsWith('{') || message.startsWith('['))
        ) {
          const parsed = JSON.parse(message);
          setBalanceState(parsed);
        } else {
          console.warn('Non-JSON message received:', message);
        }
      } catch (err) {
        console.error('Error parsing notify socket data:', err);
      }
    };
    balanceSocket.addEventListener('message', handler);
    return () => {
      balanceSocket.removeEventListener('message', handler);
    };
  }, [balanceSocket]);

  console.log('squadLevelProgress', squadLevelState);
  return (
    <SocketContext.Provider
      value={{
        socket,
        levelPlayerSocket,
        levelPlayerState,
        squadLevelSocket,
        squadLevelState,
        notifySocket,
        notifyState,
        setSquadLevelState,
        setNotifyState,
        connectSquadLevelSocket,
        connectNotifySocket,
        disconnectSquadLevelSocket,
        disconnectNotifySocket,
        isSquadLevelConnected,
        isNotifyConnected,
        setLevelPlayerState,
        connectLevelPlayerSocket,
        disconnectLevelPlayerSocket,
        isLevelPlayerConnected,
        socketState,
        setSocketState,
        connectSocket: connect,
        disconnectSocket: disconnect,
        isConnected,
        balanceSocket,
        isBalanceConnected,
        connectBalanceSocket,
        disconnectBalanceSocket,
        balanceState,
        setBalanceState,
      }}
    >
      {children}
    </SocketContext.Provider>
  );
};

export const useSocketContext = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocketContext must be used within a SocketProvider');
  }
  return context;
};
