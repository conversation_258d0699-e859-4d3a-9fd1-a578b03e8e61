import { FC, useState, ReactNode } from 'react';
import { Info } from 'lucide-react';
import InfoModal from './InfoModal';

interface InfoButtonProps {
  rules: ReactNode;
  title?: string;
  className?: string;
}

const InfoButton: FC<InfoButtonProps> = ({ rules, title, className = '' }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <button 
        onClick={() => setIsModalOpen(true)}
        className={`relative inline-flex items-center justify-center ${className}`}
        aria-label="Game information"
      >
        <div className="absolute inset-0 bg-purple-500/30 rounded-full animate-ping"></div>
        <div className="relative z-10 bg-purple-600 rounded-full p-2">
          <Info size={18} className="text-white" />
        </div>
      </button>

      <InfoModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        rules={rules}
        title={title}
      />
    </>
  );
};

export default InfoButton;