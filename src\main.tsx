import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { AuthProvider } from './auth/AuthContext.tsx';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CoinsProvider } from './hooks/useCoinsQuery.tsx';
import { SocketProvider } from './context/socketProvider.tsx';

// Ultra-fast QueryClient config
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 60 * 1000, // 10 minutes
      gcTime: 15 * 60 * 1000, // 15 minutes
      retry: 0, // No retries for faster initial load
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchInterval: false,
      networkMode: 'online'
    },
    mutations: {
      retry: 1
    }
  },
});

// Mark page as loaded for CSS optimizations
window.addEventListener('load', () => {
  document.body.classList.add('loaded');
});

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <AuthProvider>
      <SocketProvider>
        <QueryClientProvider client={queryClient}>
          <CoinsProvider>
            <App />
          </CoinsProvider>
        </QueryClientProvider>
      </SocketProvider>
    </AuthProvider>
  </StrictMode>
);
