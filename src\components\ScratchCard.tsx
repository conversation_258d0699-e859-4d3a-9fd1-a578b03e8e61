import React, { useState, useEffect, useRef } from 'react';
import { Gift, CheckCircle } from 'lucide-react';
import { useMutation, useSuspenseQuery } from '@tanstack/react-query';
import { useAuth } from '../auth/AuthContext';
import {
  getScratchCardPrice,
  placeScratchCardBet,
} from '../api/scratchCardService';
import { CustomToast } from '../utils/validations/customeToast';
import winSound from '../Sounds/Scratch/scratch_win.mp3';
import BackGroundSound from '../Sounds/Scratch/Scratch_Scratch_Sound.mp3';
import SoundManager from '../components/soundManager/SoundManager';
import { IMAGES } from '../constant/image';
import { AnimatedCard } from './AnimatedCard/AnimatedCard';
import { preloadImage, promisifySuspense } from '../utils/helper';
import { PzPopup } from './Shared/PzPopup';
import { PzButton } from './Shared/PzButton';
import { PzErrorPopup } from './Shared/PzErrorPopup';
import { AnimatedInfoButton } from '../components/Shared/AnimatedInfoButton';
import { GameRulesModal } from '../components/Shared/GameRulesModal';

interface ScratchCardProps {
  onClose: () => void;
  onWin: (reward: { type: string; amount: number }) => void;
  balance: number | null;
  setRevealBalance: (arg: boolean) => void;
}

type CardIndex = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9';

// Map API symbols to icons
const SYMBOL_MAP = {
  scratch_cent: {
    icon: IMAGES.DOLLAR_100_FRAMES,
    color: '#f97316',
    label: 'Cent',
  },
  scratch_dollar: {
    icon: IMAGES.DOLLAR_FRAMES,
    color: '#fbbf24',
    label: 'Dollar',
  },
  scratch_cup: {
    icon: IMAGES.TOKEN_100_FRAMES,
    color: '#3b82f6',
    label: 'Cup',
  },
  scratch_car: { icon: IMAGES.TOKEN_FRAMES, color: '#ec4899', label: 'Car' },
  scratch_diamond: {
    icon: IMAGES.ARTIME_FRAMES,
    color: '#8b5cf6',
    label: 'Diamond',
  },
  scratch_crawn: { icon: IMAGES.CAP_FRAMES, color: '#10b981', label: 'Crown' },
};

const CARD_SCRATCH_TIME = 1000;

const winSymbolSequences = [
  [0, 1, 2],
  [3, 4, 5],
  [6, 7, 8],
  [0, 3, 6],
  [1, 4, 7],
  [2, 5, 8],
  [0, 4, 8],
  [2, 4, 6],
];

const ScratchCard: React.FC<ScratchCardProps> = ({
  onWin,
  onClose,
  balance,
  setRevealBalance,
}) => {
  const { accessToken } = useAuth();
  const revealCardTimeoutRef = useRef<number>(0);

  const [cost, setCost] = useState<number>(0);
  const [isPurchased, setIsPurchased] = useState(false);
  const [showPurchaseError, setShowPurchaseError] = useState(false);
  const [hasRevealedAll, setHasRevealedAll] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [reward, setReward] = useState<{ type: string; amount: number } | null>(
    null
  );
  const [board, setBoard] = useState<string[][]>([]);
  const [winningSymbol, setWinningSymbol] = useState<string>('');
  const [matchCells, setMatchCells] = useState<number[][] | null>(null);
  const [prize, setPrize] = useState<string>('0');
  const containerRef = useRef<HTMLDivElement>(null);
  const [revealCard, setRevealCard] = useState(false);
  const [isPlayingAgain, setIsPlayingAgain] = useState(false);
  const [didWin, setDidWin] = useState(false);
  const [animatedCardIndexes, setAnimatedCardIndexes] = useState<number[]>([]);
  const [showRulesModal, setShowRulesModal] = useState(false);

  const {
    data: scratchCardPrice,
    refetch: refetchPrice,
    isLoading: isPriceLoading,
    isError: isPriceError,
  } = useSuspenseQuery({
    queryKey: ['scratchCardPrice'],
    queryFn: () => {
      if (!accessToken) throw new Error('Access token missing');
      return getScratchCardPrice(accessToken);
    },
    refetchOnMount: true,
  });

  const placeBetMutation = useMutation({
    mutationFn: async () => {
      if (!accessToken) throw new Error('Access token missing');
      const result = await placeScratchCardBet(accessToken);

      console.log('🚀 bet result:', result);
      return result;
    },
    onSuccess: (data) => {
      setBoard(data.board);
      setWinningSymbol(data.winning_symbol);
      setMatchCells(data.match_cells);
      setPrize(data.prize);
      setIsPurchased(true);

      if (data.prize !== '0' && parseInt(data.prize) > 0) {
        const prizeAmount = parseInt(data.prize);
        setReward({ type: 'coins', amount: prizeAmount });
      }
      CustomToast('success', `Card purchased for ${data.bet_amount} bucks`);
    },
    onError: () => {
      CustomToast('error', 'Failed to place bet.');
    },
  });

  useEffect(() => {
    // Preload all animated images to speed up their appearance
    [
      IMAGES.SCRATCH_GAME_WELCOME,
      IMAGES.CAP_FRAMES,
      IMAGES.TOKEN_FRAMES,
      IMAGES.ARTIME_FRAMES,
      IMAGES.DOLLAR_FRAMES,
      IMAGES.TOKEN_100_FRAMES,
      IMAGES.DOLLAR_100_FRAMES,
    ].forEach(preloadImage);
  }, []);

  useEffect(() => {
    if (scratchCardPrice?.price) {
      setCost(scratchCardPrice.price);
    }
  }, [scratchCardPrice]);

  useEffect(() => {
    isPriceError && CustomToast('error', 'Failed to get price');
  }, [isPriceError]);

  // Modified to show confirmation dialog first
  const handlePurchaseClick = () => {
    setRevealBalance(false);
    if (Number(balance) >= cost) {
      handleConfirmPurchase();
    } else {
      setShowPurchaseError(true);
    }
  };

  const scratchCardRules = [
    {
      title: 'Purchase a Card',
      description:
        'Click "Purchase Card" to buy a scratch card using your balance. You must have enough bucks to play.',
    },
    {
      title: 'Scratch the Card',
      description:
        "Scratch any of the 9 hidden cards by clicking or tapping. You can reveal them one at a time or click 'Reveal All' to scratch them instantly.",
    },
    {
      title: 'Win by Matching',
      description:
        'If you match three identical symbols on the grid, you win a prize. The prize amount depends on the symbol matched.',
    },
    {
      title: 'Claim Your Reward',
      description:
        'If you win, the prize is automatically added to your account. The winning cards will be highlighted.',
    },
    {
      title: 'Try Again',
      description:
        'Click "Play Again" to purchase a new card and test your luck once more!',
    },
    {
      title: 'Game History',
      description:
        'You can review your wins, losses, and rewards in your play history section.',
    },
  ];

  // Actual purchase function after confirmation
  const handleConfirmPurchase = () => {
    // Reset all game states before starting a new game
    setHasRevealedAll(false);
    setShowResult(false);
    setReward(null);
    setBoard([]);
    setWinningSymbol('');
    setMatchCells(null);
    setPrize('0');
    setRevealCard(false);
    setAnimatedCardIndexes([]);

    // Place the bet - ensure board is updated which will trigger the effect to redraw canvases
    placeBetMutation.mutate();
  };

  const handleRevealAll = () => {
    if (!isPurchased || hasRevealedAll) return;
    reward && !showResult && handleWin(reward);

    setRevealBalance(true);
    setRevealCard(true);
    setAnimatedCardIndexes(Array.from({ length: 9 }, (_, index) => index));
    setHasRevealedAll(true);
    setShowResult(true);
    setTimeout(() => {
      setRevealCard(false);
      setDidWin(false);
    }, CARD_SCRATCH_TIME);
  };

  // New function to handle play again - directly show purchase confirmation
  const handlePlayAgain = () => {
    setRevealBalance(false);
    handleConfirmPurchase();
    setIsPlayingAgain(true);

    // Re-fetch price in case it changed
    refetchPrice().then(() => {
      // Reset playing again flag
      setIsPlayingAgain(false);

      // Show confirmation directly instead of going back to purchase UI
      if (Number(balance) < cost) {
        setShowPurchaseError(true);
        // Reset states to show purchase UI since they don't have enough coins
        setIsPurchased(false);
        setHasRevealedAll(false);
        setShowResult(false);
        setReward(null);
        setBoard([]);
        setWinningSymbol('');
        setMatchCells(null);
        setPrize('0');
        setRevealCard(false);
        setTimeout(() => setShowPurchaseError(false), 3000);
      }
    });
  };

  // Convert board to flat array for rendering
  const flatBoard = board.length > 0 ? board.flat() : Array(9).fill(null);

  // Helper to check if a cell is part of the winning combination
  const isWinningCell = (index: number): boolean => {
    if (!matchCells) {
      // If matchCells is not provided, check if this cell has the winning symbol
      if (winningSymbol && flatBoard[index] === winningSymbol) {
        return true;
      }
      return false;
    }

    const row = Math.floor(index / 3);
    const col = index % 3;

    return matchCells.some((cell) => cell[0] === row && cell[1] === col);
  };

  const handleWin = (reward: { type: string; amount: number }) => {
    setDidWin(true);
    onWin?.(reward);
    setTimeout(() => {
      setDidWin(false);
    }, CARD_SCRATCH_TIME);
  };

  const checkGameProgress = (revealedCardIndexes: number[]) => {
    const isAllCardsRevealed = revealedCardIndexes.length === flatBoard.length;

    // Check is all winning symbols already revealed to reveal all the rest cards
    const isWinSequence =
      winningSymbol && revealedCardIndexes.length > 2
        ? winSymbolSequences.some((sequence) =>
            sequence.every(
              (symbolIndex) =>
                revealedCardIndexes.includes(symbolIndex) &&
                flatBoard[symbolIndex] === winningSymbol
            )
          )
        : false;

    (isAllCardsRevealed || isWinSequence) && handleRevealAll();
  };

  const handleStartScratch = (index: number) => {
    clearTimeout(revealCardTimeoutRef.current);
    revealCardTimeoutRef.current = window.setTimeout(
      () => setRevealCard(false),
      CARD_SCRATCH_TIME
    );
    setAnimatedCardIndexes((prevIndexes) => [...prevIndexes, index]);
    setRevealCard(true);
    checkGameProgress([...animatedCardIndexes, index]);
  };
  const handleEndScratch = () => setRevealCard(false);

  // Create Promise for balance Suspense from parent component
  promisifySuspense(Number.isFinite(balance));

  return (
    <PzPopup
      title={
        <div className="flex items-center">
          Scratch Card
          <AnimatedInfoButton
            onClick={() => setShowRulesModal(true)}
            label="Rules"
            size={18}
            className="ml-2"
          />
        </div>
      }
      onClose={onClose}
    >
      <SoundManager
        sounds={{
          background: BackGroundSound,
          win: winSound,
        }}
        // loop the background while the game is running:
        backgroundKey={revealCard ? 'background' : null}
        // play one of these exactly once when it changes:
        playKey={didWin ? 'win' : undefined}
        volumes={{
          background: 0.4,
          win: 1.0,
        }}
      />

      {!isPurchased ? (
        <div className="text-center">
          <img
            src={IMAGES.SCRATCH_GAME_WELCOME}
            alt="Diamond gift"
            style={{
              width: 130,
              height: 120,
              transform: 'rotate(-3.5deg)',
              margin: '2rem auto',
            }}
          />
          <h3
            className="text-4xl mb-2"
            style={{
              fontFamily: 'Bebas Neue',
            }}
          >
            TRY YOUR LUCK!
          </h3>
          <p className="font-[Poppins] text-xs text-white/60 mb-8">
            Purchase a scratch card for{' '}
            <span className="font-bold text-white">{cost}</span> bucks
            <br />
            Win up to <span className="font-bold text-white">5000</span> bucks!
          </p>
          <PzButton
            text={
              placeBetMutation.isPending
                ? 'Processing...'
                : isPlayingAgain
                ? 'Loading...'
                : 'Purchase Card'
            }
            isDisabled={
              isPriceLoading || placeBetMutation.isPending || isPlayingAgain
            }
            onClick={handlePurchaseClick}
          />
          <div className="text-sm m-auto text-center mt-2 font-[Anton]">
            Purchase a scratch card for {cost} bucks
          </div>
        </div>
      ) : (
        <>
          <div ref={containerRef} className="grid grid-cols-3 gap-2 mt-8 mb-8">
            {flatBoard.map((symbol, index) => {
              // Get the icon component for this symbol
              const symbolInfo = SYMBOL_MAP[
                symbol as keyof typeof SYMBOL_MAP
              ] || {
                icon: Gift,
                color: '#64748b',
              };

              const cardIndex = String(index + 1) as CardIndex;
              const isWinning = isWinningCell(index);
              const showAnimatedCard = animatedCardIndexes.includes(index);

              return (
                <div key={index} className={`aspect-square relative`}>
                  {/* Icon Container */}
                  {showAnimatedCard ? (
                    <AnimatedCard
                      image={symbolInfo.icon}
                      {...(isWinning &&
                        hasRevealedAll && {
                          className:
                            'border-2 border-solid border-yellow-400 rounded-3xl',
                        })}
                    />
                  ) : (
                    <div
                      style={{
                        width: 106,
                        height: 106,
                        backgroundImage: `url(${
                          IMAGES[`SCRATCH_CARD_${cardIndex}`]
                        })`,
                        backgroundSize: 'cover',
                        borderRadius: '1.5rem',
                      }}
                      className="absolute inset-0 w-full h-full cursor-pointer touch-none z-20"
                      onMouseDown={() => handleStartScratch(index)}
                      onMouseUp={handleEndScratch}
                      onTouchStart={() => handleStartScratch(index)}
                      onTouchEnd={handleEndScratch}
                    />
                  )}
                </div>
              );
            })}
          </div>

          {showResult && reward && parseInt(prize) > 0 && (
            <div className="text-center mb-4 animate-bounce">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <CheckCircle size={32} className="text-green-400" />
              </div>
              <p className="text-xl font-bold text-green-400">
                You won {prize} bucks!
              </p>
            </div>
          )}

          {showResult && (!reward || parseInt(prize) <= 0) && (
            <div className="text-center mb-4 animate-fade-in">
              <p className="text-lg font-medium text-white/80">
                No win this time. Try again!
              </p>
            </div>
          )}

          {/* Conditional rendering for Reveal All or Play Again button */}
          {hasRevealedAll ? (
            <PzButton text="Play Again" onClick={handlePlayAgain} />
          ) : (
            <PzButton text="Reveal All" onClick={handleRevealAll} />
          )}
        </>
      )}
      {showPurchaseError && (
        <PzErrorPopup
          setShowFundsErrorPopup={setShowPurchaseError}
          cost={cost}
        />
      )}
      <GameRulesModal
        isOpen={showRulesModal}
        onClose={() => setShowRulesModal(false)}
        gameTitle="Scratch Card"
        rules={scratchCardRules}
      />
    </PzPopup>
  );
};

export default ScratchCard;
