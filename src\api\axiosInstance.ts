

import axios from 'axios'
import { toast } from 'react-toastify'

const axiosInstance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
})

// Request interceptor for caching
axiosInstance.interceptors.request.use((config) => {
    // Add cache headers for GET requests
    if (config.method === 'get') {
        config.headers['Cache-Control'] = 'max-age=300';
    }
    return config;
});

axiosInstance.interceptors.response.use(
    (response) => response,
    (error) => {
        if (
            error.response &&
            error.response.status === 401 &&
            error.response.data?.message?.toLowerCase().includes('invalid or expired token')
        ) {
            toast.error(error.response.data?.message)
            localStorage.removeItem('access_token')
            window.location.href = '/auth'
        }
        return Promise.reject(error)
    }
)

export default axiosInstance

