import axios from './axiosInstance'
import {
PLACE_SCRATCH_CARD_BET_AND_GET,
GET_SCRATCH_CARD_PRICE
} from './auth';

export interface HistoryData {
  page: number;
  per_page: number;
}

export const placeScratchCardBet = async (token: string) => {
  const response = await axios.post(PLACE_SCRATCH_CARD_BET_AND_GET,null, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};
export const scratchCardHistory = async (data: HistoryData, token: string) => {
  const response = await axios.get(PLACE_SCRATCH_CARD_BET_AND_GET, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: {
      page: data.page,
      perPage: data.per_page,
    },
  });
  return response.data;
};
export const getScratchCardPrice = async (token: string) => {
    const response = await axios.get(GET_SCRATCH_CARD_PRICE, {
      headers: {
        Authorization: `Bear<PERSON> ${token}`,
      }
    });
  
    return response.data;
  };