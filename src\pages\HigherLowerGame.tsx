/**
 * HigherLowerGame Component
 * A card-guessing game where users bet coins and guess if the next card is higher or lower.
 *
 * @component
 * @param {Object} props
 * @param {{ coins: number }} props.balance - User's coin balance
 * @param {(newBalance: { coins: number }) => void} props.onBalanceChange - Callback when balance changes
 */

import React, { useState, useEffect, useRef } from 'react';
import { ChevronLeft, Coins, ArrowUp, ArrowDown, History } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  placeBetForQuickHustles,
  SelectForQuickHustels,
  getQuickHustlesHistory,
  WinStatus,
} from '../api/QuickHustlesService';
import { useAuth } from '../auth/AuthContext';
import { useMutation, useSuspenseInfiniteQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { IMAGES } from '../constant/image';
import winSound from '../Sounds/CryptoKing/win_sound.m4a';
import BackGroundSound from '../Sounds/QuickHustle/card_turn.mov';
import SoundManager from '../components/soundManager/SoundManager';
import Transactions from '../components/Transactions';
import { COLORS } from '../constant/theming';
import { PzButton } from '../components/Shared/PzButton';
import csvCardsData from '../data/high-low_multiplier_table.csv?raw';
import { parseCSV, promisifySuspense } from '../utils/helper';
import { APP_STRINGS } from '../constant/strings';
import { PzErrorPopup } from '../components/Shared/PzErrorPopup';
import { DEFAULT_BET } from '../constant/numbers';
import { AnimatedInfoButton } from '../components/Shared/AnimatedInfoButton';
import { GameRulesModal } from '../components/Shared/GameRulesModal';

// Utility to format coins
const formatCoins = (value: number | string) => Number(value).toLocaleString();

type CsvCardsParsedData = {
  card: string;
  higherMultiplier: string;
  lowerMultiplier: string;
  weight: string;
}[];

interface HigherLowerGameProps {
  onWin: (reward: { type: string; amount: number }) => void;
  balance: number | null;
  setRevealBalance: (arg: boolean) => void;
}

const HigherLowerGame: React.FC<HigherLowerGameProps> = ({
  onWin,
  balance,
  setRevealBalance,
}) => {
  const { accessToken } = useAuth();
  const navigate = useNavigate();
  const csvCardsParsedData = useRef<CsvCardsParsedData>([]);

  const [showTransactionModal, setShowTransactionModal] = useState(false);
  // Game State
  const [betAmount, setBetAmount] = useState<number | string>(DEFAULT_BET);
  const [roundId, setRoundId] = useState<string>('');
  const [gameStatus, setGameStatus] = useState<string>('');
  const [userId, setUserId] = useState<string>('');
  const [currentCard, setCurrentCard] = useState<string>('');
  const [nextCard, setNextCard] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [showResult, setShowResult] = useState<boolean>(false);
  const [showNextCard, setShowNextCard] = useState<boolean>(false);
  const [showStartButton, setShowStartButton] = useState<boolean>(true);
  const [didWin, setDidWin] = useState(false);
  const [backSound, setBackSound] = useState(false);
  const [winAmount, setWinAmount] = useState<number>(0);
  const [cardMultipliers, setCardMultipliers] = useState<{
    higher?: string;
    lower?: string;
  }>({});
  const [showFundsErrorPopup, setShowFundsErrorPopup] = useState(false);
  const [showRulesModal, setShowRulesModal] = useState(false);

  // Reset game after result is shown
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showResult && gameStatus) {
      timer = setTimeout(() => {
        setShowStartButton(true);
        setShowResult(false);
        setIsPlaying(false);
      }, 2000);
    }
    return () => clearTimeout(timer);
  }, [showResult, gameStatus]);

  // Fetch game history using infinite query
  const {
    data: historyPages,
    hasNextPage,
    refetch,
    isFetchingNextPage,
  } = useSuspenseInfiniteQuery({
    queryKey: ['history'],
    queryFn: ({ pageParam = 1 }) => {
      if (!accessToken) throw new Error('Access token missing');
      return getQuickHustlesHistory(
        { page: pageParam as number, per_page: 10, version: 'v1' },
        accessToken
      );
    },

    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      // Determine if more pages exist
      if (
        !lastPage?.data?.total_pages ||
        allPages.length >= lastPage.data.total_pages
      )
        return undefined;
      return allPages.length + 1;
    },
  });


  const quickHustleRules = [
  {
    title: "Start the Game",
    description:
      "Enter your desired bet amount and click the 'Start Game' button to reveal the first card."
  },
  {
    title: "Make a Guess",
    description:
      "After the first card is shown, guess whether the next card will be higher or lower by clicking the respective button."
  },
  {
    title: "Reveal the Result",
    description:
      "The next card is revealed. If your guess is correct, you win the round and earn a reward based on the multiplier."
  },
  {
    title: "Multipliers",
    description:
      "Each card has different multipliers for higher and lower guesses. These are displayed below the guess buttons."
  },
  {
    title: "Winning & Losing",
    description:
      "If your guess is correct, you win and earn coins based on the multiplier. If not, you lose the bet amount."
  },
  {
    title: "Game History",
    description:
      "Your recent plays are displayed in the history section, including cards drawn, guess made, and result."
  }
];


  // Scroll to top on mount and refetch history
  useEffect(() => {
    csvCardsParsedData.current = parseCSV(csvCardsData) as CsvCardsParsedData;
    window.scrollTo(0, 0);
    refetch();
  }, []);

  useEffect(() => {
    const cardData = csvCardsParsedData.current.find(
      (cardData) => cardData.card === currentCard
    );
    setCardMultipliers({
      higher: cardData?.higherMultiplier,
      lower: cardData?.lowerMultiplier,
    });
  }, [currentCard]);

  // Mutation for placing a bet
  const placeBetMutation = useMutation({
    mutationFn: async () => {
      if (!accessToken) throw new Error('Access token missing');
      const result = await placeBetForQuickHustles(
        { bet_amount: Number(betAmount) },
        accessToken
      );
      return result;
    },
    onSuccess: (res) => {
      if (res) {
        setCurrentCard(res?.data?.first_card);
        setRoundId(res?.data?.id);
        setUserId(res?.data?.user_id);
        setIsPlaying(true);
        setShowNextCard(false);
        setShowResult(false);
        setShowStartButton(false);
        setGameStatus('');
      }
    },
  });

  useEffect(() => {
    let soundTimer: NodeJS.Timeout;

    if (showNextCard && backSound) {
      // Stop the background sound after card is fully revealed (adjust timing as needed)
      soundTimer = setTimeout(() => {
        setBackSound(false);
      }, 1000); // 1 second - adjust this duration based on your card flip animation
    }

    return () => clearTimeout(soundTimer);
  }, [showNextCard, backSound]);

  // Mutation for guessing HIGHER/LOWER
  // Select High/Low
  const QuickHustlesSelect = useMutation({
    mutationFn: async (guess: 'HIGHER' | 'LOWER') => {
      if (!accessToken) throw new Error('token is missing');
      const result = await SelectForQuickHustels(
        {
          id: roundId,
          user_guess: guess,
          user_id: userId,
        },
        accessToken
      );
      result.data.won_status === WinStatus.WON &&
        onWin({ type: 'coins', amount: Number(result.data.won_amount) });

      // Start background sound when card reveal begins and reveal balance
      setBackSound(true);
      setRevealBalance(true);
      return result;
    },
    onSuccess: (res) => {
      if (Number(res?.data?.won_amount) > 0) {
        setWinAmount(Number(res?.data?.won_amount));
        setDidWin(true); // Set win state for win sound
      } else {
        setDidWin(false);
      }

      setNextCard(res?.data?.second_card);
      setIsPlaying(true);
      setShowNextCard(true);
      setShowResult(true);
      setGameStatus(res?.data?.won_status);

      // Don't stop backSound here - let the useEffect handle it
      refetch();
    },
  });

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showResult && gameStatus) {
      timer = setTimeout(() => {
        setShowStartButton(true);
        setShowResult(false);
        setIsPlaying(false);
        setBackSound(false); // Reset sound state
        setDidWin(false); // Reset win sound state
      }, 2000);
    }
    return () => clearTimeout(timer);
  }, [showResult, gameStatus]);
  /**
   * Handle user guess
   * @param {'HIGHER' | 'LOWER'} guess
   */
  const handleGuess = (guess: 'HIGHER' | 'LOWER') => {
    QuickHustlesSelect.mutate(guess);
  };

  /**
   * Map any letter 'A'–'Z' (case‐insensitive) into 1–13, then return the matching CARD_<n> URL.
   *   A→1, B→2, …, M→13, N→1, O→2, …, Z→12
   *
   * If the input isn't a single A–Z, falls back to CARD_1.
   */
  const getCardImage = (value: number | string) => {
    let index = 1; // default to Ace

    if (typeof value === 'number') {
      // If someone already passed a number 1–13, use it directly (clamp between 1 and 13)
      const num = Math.floor(value);
      index = num >= 1 && num <= 13 ? num : 1;
    } else {
      // If it's a string, check if it's a single letter A–Z
      const v = value.toUpperCase().trim();
      if (v.length === 1 && v >= 'A' && v <= 'Z') {
        // 'A'.charCodeAt(0) = 65. Subtract 65 → 0, add 1 → 1 (Ace).
        // 'B'→2, …, 'M'→13. 'N' charCode 78 → (78–65)%13 = 0 → index 1 again, etc.
        index = ((v.charCodeAt(0) - 65) % 13) + 1;
      } else {
        // anything else (e.g. "foo", "1", "AA"), just default to 1
        index = 1;
      }
    }

    // Build the key "CARD_1" … "CARD_13" and look it up
    const key = `CARD_${index}` as keyof typeof IMAGES;
    return IMAGES[key] || IMAGES.CARD_1;
  };

  /**
   * Handle manual bet change
   * @param {React.ChangeEvent<HTMLInputElement>} e
   */
  const handleBetChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setBetAmount(parseInt(e.target.value, 10) || '');

  // Halve the current bet
  const halfBet = () => setBetAmount((prev) => Math.floor(Number(prev) / 2));

  // Double the current bet (capped by available coins)
  const doubleBet = () => setBetAmount((prev) => Number(prev) * 2);

  const onClose = () => {
    setShowTransactionModal(false);
  };

  // Promisify balance data loading into parent Suspense
  promisifySuspense(Number.isFinite(balance));

  const goToHomePage = () => navigate('/');

  const onPlaceBetClick = () => {
    Number(balance) < Number(betAmount)
      ? setShowFundsErrorPopup(true)
      : placeBetMutation.mutate();
  };

  return (
    <div className="min-h-screen text-text-primary relative">
      <SoundManager
        sounds={{
          background: BackGroundSound,
          win: winSound,
        }}
        // loop the background while the game is running:
        backgroundKey={backSound ? 'background' : null}
        // play one of these exactly once when it changes:
        playKey={didWin ? 'win' : undefined}
        volumes={{
          background: 0.4,
          win: 1.0,
        }}
      />
      {/* Background Overlay */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

      <div className="max-w-4xl mx-auto relative">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={goToHomePage}
            className="text-white/60 hover:text-white transition-colors"
          >
            <ChevronLeft size={24} />
          </button>
          <h1 className="text-2xl font-bold">Quick Hustle</h1>
          <div className="ml-auto">
            <AnimatedInfoButton
              onClick={() => setShowRulesModal(true)}
              label="Rules"
              size={18}
            />
          </div>
        </div>

        {/* Game Container */}
        <div className="space-y-6">
          {/* Main Game Area */}
          <div className="space-y-6">
            {/* Cards Display */}
            <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Coins
                    size={20}
                    color={COLORS.primary}
                    className="text-yellow-400"
                  />
                  <span>{formatCoins(Number(balance))} Bucks</span>
                </div>
              </div>

              <div className="flex justify-center gap-8 items-center min-h-[220px] relative">
                {/* Current Card */}
                {isPlaying && (
                  <div className="relative w-36 h-56 rounded-xl overflow-hidden shadow-xl transform transition-transform hover:scale-15">
                    {/* Background Image */}
                    <div
                      className="absolute inset-0 bg-cover bg-center z-0"
                      style={{
                        backgroundImage: `url(${getCardImage(currentCard)})`,
                      }}
                    />

                    {/* Dark Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-b from-black/10 to-transparent z-10" />

                    {/* Colored Gradient Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-900 to-indigo-900 opacity-30 z-20" />

                    {/* Card Name */}
                    <div className="absolute top-4 left-4 text-3xl font-bold text-white z-30">
                      {currentCard}
                    </div>
                  </div>
                )}

                {/* Game Status Display */}

                {/* Next Card */}
                {isPlaying && (
                  <div className="relative">
                    <div
                      className={`w-36 h-56 rounded-xl overflow-hidden shadow-xl transform transition-all duration-500 ${
                        showNextCard
                          ? 'rotate-0 opacity-100'
                          : 'rotate-180 opacity-0'
                      }`}
                    >
                      {/* Background Image Layer */}
                      <div
                        className="absolute inset-0 bg-cover bg-center z-0"
                        style={{
                          backgroundImage: `url(${getCardImage(nextCard)})`,
                        }}
                      />

                      {/* Dark Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-b from-black/50 to-transparent z-10" />

                      {/* Color Overlay (optional: can add opacity here to make image more visible) */}
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-900 to-indigo-900 opacity-30 z-20" />

                      {/* Card Text */}
                      <div className="absolute top-4 left-4 text-3xl font-bold text-white z-30">
                        {nextCard}
                      </div>
                    </div>

                    {/* Placeholder "?" when card is hidden */}
                    {!showNextCard && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-5xl">?</div>
                      </div>
                    )}
                  </div>
                )}
                {showResult && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span
                      className={`text-3xl font-bold animate-pulse ${
                        gameStatus === 'WON' ? 'text-green-400' : 'text-red-400'
                      }`}
                    >
                      {gameStatus === 'WON'
                        ? 'WON +' + formatCoins(winAmount)
                        : 'LOST'}
                    </span>
                  </div>
                )}
                {!isPlaying && !showResult && (
                  <div className="text-center text-white/60">
                    <p className="text-xl mb-2">
                      Place your bet to start playing
                    </p>
                    <p>Guess if the next card will be higher or lower</p>
                  </div>
                )}
              </div>
            </div>

            <PzButton
              text="Start Game"
              onClick={onPlaceBetClick}
              isDisabled={isPlaying}
            />

            {/* Controls */}
            <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
              {showStartButton ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm text-white/60 mb-2">
                      Bet Amount
                    </label>

                    <div className="relative">
                      <input
                        type="number"
                        value={betAmount}
                        onChange={handleBetChange}
                        className={`w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 focus:outline-none focus:border-[${COLORS.primary}] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]`}
                      />
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-2">
                        <button
                          onClick={halfBet}
                          className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors disabled:opacity-50"
                        >
                          ½
                        </button>
                        <button
                          onClick={doubleBet}
                          className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors disabled:opacity-50"
                        >
                          2×
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <button
                      onClick={() => handleGuess('HIGHER')}
                      disabled={showResult}
                      className={`py-3 w-full rounded-lg font-medium transition-colors flex items-center justify-center gap-2 ${
                        showResult
                          ? 'bg-white/10 text-white/40 cursor-not-allowed'
                          : 'bg-green-500 hover:bg-green-400'
                      }`}
                    >
                      <ArrowUp size={20} />
                      Higher
                    </button>
                    <div className="pt-2 text-center text-sm text-white/60">
                      {APP_STRINGS.MULTIPLIER}: {cardMultipliers.higher}
                    </div>
                  </div>

                  <div>
                    <button
                      onClick={() => handleGuess('LOWER')}
                      disabled={showResult}
                      className={`py-3 w-full rounded-lg font-medium transition-colors flex items-center justify-center gap-2 ${
                        showResult
                          ? 'bg-white/10 text-white/40 cursor-not-allowed'
                          : 'bg-red-500 hover:bg-red-400'
                      }`}
                    >
                      <ArrowDown size={20} />
                      Lower
                    </button>
                    <div className="pt-2 text-center text-sm text-white/60">
                      {APP_STRINGS.MULTIPLIER}: {cardMultipliers.lower}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* History */}
          <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
            <h2 className="text-lg font-[Anton] mb-4 flex items-center gap-2">
              <History
                size={20}
                color={COLORS.primary}
                className="text-yellow-400"
              />
              Game History
            </h2>

            <div className="space-y-4">
              {historyPages?.pages[0]?.data?.Histories &&
                historyPages?.pages
                  .flatMap((page) => page.data?.Histories || [])
                  .map((game, index) => {
                    const profit =
                      game.won_status === 'LOSE'
                        ? parseFloat(game.bet_amount)
                        : parseFloat(game.won_amount);
                    return (
                      <div
                        key={index}
                        className={`p-4 rounded-lg text-[Poppins] ${
                          game?.won_status === 'WON'
                            ? 'bg-green-500/10'
                            : 'bg-red-500/10'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">
                              {game.first_card}
                            </span>
                            <span className="text-white/60">→</span>
                            <span className="font-medium">
                              {game.second_card}
                            </span>
                          </div>

                          {game?.won_status === 'LOSE' ? (
                            <span className={`font-medium text-red-400`}>
                              -{profit}
                            </span>
                          ) : (
                            <span className={`font-medium text-green-400 `}>
                              +{profit}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center justify-between text-sm text-white/60">
                          <span>Bet: {game.bet_amount}</span>
                          <span>Guessed: {game.user_guessed}</span>
                          <span>{dayjs(game.timestamp).format('hh:mm A')}</span>
                        </div>
                      </div>
                    );
                  })}

              {hasNextPage && (
                <div className="flex justify-center pt-4">
                  <PzButton
                    text="View All"
                    onClick={() => {
                      setShowTransactionModal(true);
                    }}
                    isDisabled={isFetchingNextPage}
                  />
                </div>
              )}
              {!hasNextPage &&
                historyPages?.pages?.flatMap((page) => page.data).length ===
                  0 && (
                  <div className="text-center text-white/40 py-4">
                    No game history yet
                  </div>
                )}
            </div>
          </div>
        </div>
      </div>
      {showTransactionModal && <Transactions onClose={onClose} />}
      {showFundsErrorPopup && (
        <PzErrorPopup
          setShowFundsErrorPopup={setShowFundsErrorPopup}
          cost={Number(betAmount)}
        />
      )}
      <GameRulesModal
        isOpen={showRulesModal}
        onClose={() => setShowRulesModal(false)}
        gameTitle="Quick Hustle"
        rules={quickHustleRules}
      />
    </div>
  );
};

export default HigherLowerGame;
