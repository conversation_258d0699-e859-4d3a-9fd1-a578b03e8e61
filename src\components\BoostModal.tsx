import React, { useState, useEffect } from 'react';
import { X, Zap, Facebook, Twitter, Instagram } from 'lucide-react';
import { postRewardClaim } from '../api/rewardsService';
import { useAuth } from '../auth/AuthContext';
import { CustomToast } from '../utils/validations/customeToast';
import { RewardGameType } from '../types/Rewards';
import { useCoins } from '../hooks/useCoinsQuery'

/**
 * Props for the BoostModal component
 * @interface BoostModalProps
 * @property {function} onClose - Function to close the modal
 * @property {function} onWatchAd - Function to handle watch ad action
 * @property {function} onShare - Function to handle share action with platform type
 */
interface BoostModalProps {
  onClose: () => void;
  onWatchAd: () => void;
  onShare: (platform: RewardGameType) => void;
}

/**
 * Available boost options with rewards
 * @constant {Array<BoostOption>}
 */
const BOOST_OPTIONS = [
  {
    id: RewardGameType.REWARDED_AD,
    title: 'Watch Video',
    description: 'Watch a short video to earn Bucks',
    icon: Zap,
    xp: 50,
  },
  {
    id: RewardGameType.FACEBOOK_SHARE,
    title: 'Share on Facebook',
    description: 'Share your progress with friends',
    icon: Facebook,
    xp: 100,
  },
  {
    id: RewardGameType.TWITTER_SHARE,
    title: 'Share on Twitter',
    description: 'Tweet about your achievements',
    icon: Twitter,
    xp: 100,
  },
  {
    id: RewardGameType.INSTAGRAM_SHARE,
    title: 'Share on Instagram',
    description: 'Post your game highlights',
    icon: Instagram,
    xp: 100,
  },
];

/**
 * Modal component that displays various ways to boost progress
 * @component
 * @param {BoostModalProps} props - Component props
 * @returns {React.ReactElement} Rendered component
 */
const BoostModal: React.FC<BoostModalProps> = ({ onClose, onWatchAd, onShare }) => {
  // Hooks and state
  const { userProfile } = useAuth();
  const { refetchCoins } = useCoins();
  const [isMobile, setIsMobile] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [copied, setCopied] = useState(false);

  /**
   * Check if device is mobile on mount and window resize
   */
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    // Trigger animation
    const timeout = setTimeout(() => setIsAnimating(true), 50);
    return () => {
      clearTimeout(timeout);
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Generate share URL from user's referral code
  const referralCode = userProfile?.referral_code;
  const shareUrl = referralCode ? `${window.location.origin}/auth?ref=${referralCode}` : '';

  /**
   * Handle sharing to social platforms and claim rewards
   * @param {RewardGameType} platform - Platform to share to
   * @returns {Promise<void>}
   */
  const handleShareAndClaim = async (platform: RewardGameType) => {
    if (!shareUrl) return;
    const message = `Join me on Rise & Hustle! Use my referral link: ${shareUrl}`;

    if (navigator.share) {
      try {
        navigator.share({ title: 'Rise & Hustle', text: message, url: shareUrl });
      } catch {
        // user cancelled or error
      }
    } else {
      let url = '';
      switch (platform) {
        case RewardGameType.FACEBOOK_SHARE:
          url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
            shareUrl
          )}&quote=${encodeURIComponent(message)}`;
          break;
        case RewardGameType.TWITTER_SHARE:
          url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}`;
          break;
        case RewardGameType.INSTAGRAM_SHARE:
          url = 'https://www.instagram.com/';
          break;
        default:
          break;
      }
      if (url) window.open(url, '_blank', 'noopener,noreferrer');
    }

    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      CustomToast('success', 'Link copied to clipboard!');
    } catch {
      CustomToast('error', 'Could not copy link');
    }

    if (userProfile?.user_id) {
      postRewardClaim({
        user_id: userProfile.user_id,
        game: platform,
      }).then((res) => {
        if (res?.success === true) {
          setTimeout(() => {
          refetchCoins(); // Delay call by 1 second
        }, 2000);
        } else {
          console.error('Claim failed', res?.message);
          CustomToast('error', 'Failed to record share reward');
        } 
      }).catch((err) => {
        console.error('Claim failed', err);
        CustomToast('error', 'Failed to record share reward');
      });
    } else {
      console.error('User is not logged in');
      CustomToast('error', 'Please refresh page and log back in');
    }
  };

  // Dynamic class names for responsive design and animations
  const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;
  const mobileContentClass = `
    bg-gradient-to-b from-[#510957] to-black
    rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out
    ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;
  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;
  const desktopContentClass = `
    bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
    rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col
    transform transition-all duration-300 ease-out
    ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
  `;

  return (
    <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
      <div className={isMobile ? mobileContentClass : desktopContentClass}>
        <div className="p-6 flex justify-between items-center sticky top-0 rounded-t">
          <h2 className="text-xl font-bold">Boost Your Progress</h2>
          <button onClick={onClose} className="text-white/60 hover:text-white">
            <X size={24} />
          </button>
        </div>
        <div className="p-6 space-y-4">
          {BOOST_OPTIONS.map((option) => {
            const Icon = option.icon;
            return (
              <button
                key={option.id}
                onClick={() => {
                  if (option.id === RewardGameType.REWARDED_AD) {
                    onWatchAd();
                  } else {
                    handleShareAndClaim(option.id);
                    onShare(option.id);
                  }
                  onClose();
                }}
                className="w-full bg-[#131313] p-4 rounded-xl flex items-center gap-4"
              >
                <div className="bg-white/10 p-3 rounded-lg">
                  <Icon size={24} className="text-[#ED0CFF]" />
                </div>
                <div className="flex-1 text-left">
                  <h3 className="font-medium">{option.title}</h3>
                  <p className="text-sm text-white/60">{option.description}</p>
                </div>
                <div className="flex items-center gap-1 text-white">
                  <span>+{option.xp}</span>
                  <span className="uppercase">Bucks</span>
                </div>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

/**
 * Export the BoostModal component
 */
export default BoostModal;
