import React, { useEffect, useRef, useState } from 'react'
import { X, CheckCircle } from 'lucide-react'
import { toast } from 'react-toastify'
import { postRewardClaim } from '../api/rewardsService'
import { useAuth } from '../auth/AuthContext'
import { RewardGameType } from '../types/Rewards'
import NativeAdService from '../api/nativeAdService'
import webAdService, { RewardAd } from '../api/webAdService';

import { useCoins } from '../hooks/useCoinsQuery'

interface VideoAdProps {
  onClose: () => void
  onComplete?: () => void
}

declare global {
  interface Window {
    nativeHandler?: { postMessage(message: string): void }
    google?: {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ima: any
    }
  }
}

const VideoAd: React.FC<VideoAdProps> = ({ onClose, onComplete }) => {
  const { userProfile } = useAuth()
  const { refetchCoins } = useCoins()
  const isNative = typeof window.nativeHandler !== 'undefined'

  const adContainerRef = useRef<HTMLDivElement>(null)
  const videoElementRef = useRef<HTMLVideoElement | null>(null)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const adsLoaderRef = useRef<any>(null)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const adsManagerRef = useRef<any>(null)

  const [showReward, setShowReward] = useState(false)
  const rewardTimeoutRef = useRef<number>()

  // — NATIVE PATH —
  useEffect(() => {
    if (!isNative || !userProfile?.user_id) return

    NativeAdService.showRewardedAd({ userId: userProfile.user_id })

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const listener = (action: string, data: any) => {
      if (action === 'rewardAdClaimed') {
        setShowReward(true);
        onComplete?.();
        rewardTimeoutRef.current = window.setTimeout(onClose, 2000);
      } else if (action === 'rewardAdClaimFailed') {
        toast.error('Reward Ad failed ' + (data?.error ?? ""));
        onClose()
      } else if (action === 'rewardClaimSkipped') {
        toast.error('Reward Ad skipped - no reward granted');
        onClose()
      }
    }

    NativeAdService.listen(listener)
    return () => {
      NativeAdService.cleanup()
      clearTimeout(rewardTimeoutRef.current)
    }
  }, [isNative, onComplete, onClose, userProfile])

  // — WEB / IMA PATH —
  useEffect(() => {
    if (isNative) return
    if (!window.google?.ima) {
      console.error('IMA SDK not loaded')
      return
    }

    const vid = document.createElement('video')
    videoElementRef.current = vid
    vid.style.display = 'none'
    document.body.appendChild(vid)

    const adDisplayContainer = new window.google.ima.AdDisplayContainer(
      adContainerRef.current!,
      vid
    )
    adDisplayContainer.initialize()
    adsLoaderRef.current = new window.google.ima.AdsLoader(adDisplayContainer)

    adsLoaderRef.current?.addEventListener(
      window.google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (e: any) => {
        adsManagerRef.current = e.getAdsManager(vid)

        adsManagerRef.current?.addEventListener(
          window.google?.ima.AdEvent.Type.COMPLETE,
          () => {
            setShowReward(true)
            try {
              if (userProfile?.user_id) {
                postRewardClaim({
                  user_id: userProfile.user_id,
                  game: RewardGameType.REWARDED_AD,
                }).then((res) => {
                  if (res?.success === true) {
                    setTimeout(() => {
                      refetchCoins(); 
                    }, 2000);
                  } else {
                    console.error('Claim unsuccessful:', res?.message);
                  }
                }).catch((err) => console.error('Claim failed', err))
              } else {
                console.error("UserId unknown")
              }
            } catch (error) {
              console.error('Reward claim failed:', error);
            }

            onComplete?.();
            rewardTimeoutRef.current = window.setTimeout(onClose, 2000)
          }
        )

        try {
          adsManagerRef.current?.init(
            adContainerRef.current!.offsetWidth,
            adContainerRef.current!.offsetHeight,
            window.google?.ima.ViewMode.NORMAL
          )
          adsManagerRef.current?.start()
        } catch (adError) {
          console.error('AdsManager could not start', adError)
          onClose()
        }
      }
    )

    adsLoaderRef.current?.addEventListener(
      window.google.ima.AdErrorEvent.Type.AD_ERROR,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (errEvent: any) => {
        const adError = errEvent.getError()
        console.error('IMA Error code:', adError.getErrorCode())
        console.error(
          'IMA VAST error:',
          adError.getVastErrorCode(),
          adError.getMessage()
        )
        onClose()
      }
    )

    const ad: RewardAd | null = webAdService.getNextAd();
    if (ad) {
      const adsRequest = new window.google.ima.AdsRequest()
      adsRequest.adTagUrl = ad?.url
      adsRequest.linearAdSlotWidth = adContainerRef.current!.offsetWidth
      adsRequest.linearAdSlotHeight = adContainerRef.current!.offsetHeight
      adsLoaderRef.current?.requestAds(adsRequest)
    }

    return () => {
      adsManagerRef.current?.destroy()
      adsLoaderRef.current?.destroy()
      clearTimeout(rewardTimeoutRef.current)
      if (videoElementRef.current) {
        document.body.removeChild(videoElementRef.current)
      }
    }
  }, [isNative, onComplete, onClose, userProfile])

  return (
    <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl p-6 w-full max-w-lg relative">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">
            {isNative ? 'Loading Reward…' : 'Loading Video Ad…'}
          </h2>
          <button onClick={onClose} className="text-white/60 hover:text-white">
            <X size={24} />
          </button>
        </div>

        {!isNative && !showReward && (
          <div
            ref={adContainerRef}
            className="aspect-video w-full rounded-xl overflow-hidden bg-black"
          />
        )}

        {showReward && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm">
            <div className="text-center animate-fade-in">
              <CheckCircle size={48} className="text-green-400 mx-auto mb-3" />
              <p className="text-xl font-bold text-white">
                🎉 You earned 50 Bucks!
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default VideoAd
