import { <PERSON>, <PERSON>O<PERSON>, <PERSON>, Mail, Phone } from "lucide-react";
import React from "react";
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from "formik";
import * as Yup from "yup";

interface LoginFormProps {
  /**
   * Callback invoked when the user submits the login form.
   * @param identifier - The user's email address or phone number.
   * @param password - The user's password.
   */
  onLogin: (identifier: string, password: string) => void;

  /**
   * Callback to switch from the login tab to the signup tab.
   */
  onSwitchTab: () => void;

  /**
   * Callback invoked when the user clicks "Forgot Password?"
   */
  onForgotPassword: () => void;

  /**
   * Callback invoked when the user chooses to sign in with <PERSON>.
   */
  onGoogleAuth: () => void;

  /**
   * Indicates whether a login request is currently in progress.
   */
  isLoading: boolean;

  /**
   * Error message to display if login fails.
   */
  error: string;
}

interface LoginFormValues {
  /** The user's email address or phone number. */
  identifier: string;

  /** The user's password. */
  password: string;
}

/**
 * Yup validation schema for the login form.
 * - `identifier`: If it contains "@", it must pass email validation; otherwise it just must be non-empty.
 * - `password`: Must be a non-empty string.
 */
const LoginSchema = Yup.object().shape({
  identifier: Yup.string()
    .required("Email or phone is required")
    .test(
      "email-or-phone",
      "If you enter an email, it must be valid; otherwise any non-empty phone is allowed",
      (value) => {
        if (!value) return false;
        // If it looks like an email (contains "@"), enforce a valid email format:
        if (value.includes("@")) {
          return Yup.string().email().isValidSync(value);
        }
        // Otherwise, accept it as a phone (including international formats):
        return true;
      }
    ),
  password: Yup.string().required("Password is required"),
});

/**
 * LoginForm Component
 *
 * A form that allows users to log in using either:
 *   • Email + password
 *   • Phone number + password
 *
 * Includes:
 * - Single “identifier” field (email or phone) with dynamic icon
 * - Password field with show/hide toggle
 * - "Login" submit button with loading spinner
 * - "Continue with Google" button
 * - "Forgot Password?" link
 * - Link to switch to the sign-up form
 */
const LoginForm: React.FC<LoginFormProps> = ({
  onLogin,
  onSwitchTab,
  onForgotPassword,
  isLoading,
  error,
}) => {
  // State to track whether the password is visible
  const [showPassword, setShowPassword] = React.useState(false);

  // Initial form field values
  const initialValues: LoginFormValues = {
    identifier: "",
    password: "",
  };

  /**
   * Handle form submission from Formik.
   * Invokes the `onLogin` callback with identifier (email or phone) and password values.
   */
  const handleSubmit = async (
    values: LoginFormValues,
    formikHelpers: FormikHelpers<LoginFormValues>
  ) => {
    const phoneNumber = `+234${values?.identifier}`
    onLogin(phoneNumber, values.password);
    formikHelpers.setSubmitting(false);
  };

  /**
   * Utility to decide which icon to show in the identifier field:
   * - If the input contains "@", show the Mail icon.
   * - Otherwise, show the Phone icon.
   */
  const renderIdentifierIcon = (identifier: string) => {
    if (identifier.includes("@")) {
      return <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-white/40" size={18} />;
    }
    return <Phone className="absolute left-3 top-1/2 -translate-y-1/2 text-white/40" size={18} />;
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={LoginSchema}
      onSubmit={handleSubmit}

    >
      {({ errors, touched, values }) => (
        <Form className="space-y-4">
          {/* Display an error message if provided */}
          {error && <p className="text-red-500 text-sm">{error}</p>}

          {/* ----- Identifier Field (Email or Phone) ----- */}
          {/* <div className="relative">
            <label htmlFor="identifier" className="block text-sm font-normal font-[Poppins] mb-1">
              Phone Number
            </label>
            <div className="flex space-x-2">
              <div className="flex items-center px-3 rounded-l-lg border border-white/20 bg-white/10 text-white/80">
                <Phone size={16} className="mr-1" />
                <span>+234</span>
              </div>
              <Field
                id="identifier"
                type="text"
                name="identifier"
                maxLength={10}
                className={`w-full bg-white/10 border ${errors.identifier && touched.identifier
                  ? "border-red-500"
                  : "border-white/20"
                  } rounded-lg pl-10 pr-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]`}
                placeholder="+234 123 456 78 90"
              />
            </div>
            <ErrorMessage
              name="identifier"
              component="div"
              className="text-red-500 text-sm mt-1"
            />
          </div> */}

          <div>
            <label htmlFor="identifier" className="block text-sm font-normal font-[Poppins] mb-1">
              Phone Number *
            </label>
            <div className="flex space-x-2">
              <div className="flex items-center px-3 rounded-l-lg border border-white/20 bg-white/10 text-white/80">
                <Phone size={16} className="mr-1" />
                <span>+234</span>
              </div>
              <Field
                id="identifier"
                name="identifier"
                type="tel"
                placeholder="8031234567"
                maxLength={10}
                className={`w-full bg-white/10 border ${errors.identifier && touched.identifier
                  ? "border-red-500"
                  : touched.identifier && !errors.identifier
                    ? "border-green-500"
                    : "border-white/20"
                  } rounded-r-lg py-2 px-3 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF] transition-colors`}
                onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, '').slice(0, 10);
                }}
              />
            </div>
            <ErrorMessage
              name="identifier"
              component="div"
              className="text-red-500 text-sm mt-1"
            />
          </div>

          {/* ----- Password Field ----- */}
          <div className="relative">
            <label htmlFor="password" className="block text-sm font-normal font-[Poppins] mb-1">
              Password
            </label>
            <div className="relative">
              <Field
                id="password"
                type={showPassword ? "text" : "password"}
                name="password"
                className={`w-full bg-white/10 border ${errors.password && touched.password ? "border-red-500" : "border-white/20"
                  } rounded-lg pl-10 pr-10 py-2 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]`}
                placeholder="Enter your password"
              />
              <Lock
                className="absolute left-3 top-1/2 -translate-y-1/2 text-white/40"
                size={18}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-white/40 hover:text-white/60"
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
            <ErrorMessage
              name="password"
              component="div"
              className="text-red-500 text-sm mt-1"
            />
          </div>
          <div>
            <button
              type="button"
              onClick={onForgotPassword}
              className="font-normal hover:underline text-sm text-[#ED0CFF] font-[Poppins]"
            >
              Forgot Password?
            </button>
          </div>
          {/* ----- Submit Button ----- */}
          <button
            type="submit"
            disabled={isLoading}
            className={`w-full p-2 rounded font-bold flex font-normal uppercase font-[Anton] tracking-wider justify-center items-center ${isLoading ? "bg-[#ED0CFF] cursor-not-allowed" : 'bg-[#ED0CFF]'} text-white`}
          >
            {isLoading
              ? <span className="animate-spin h-5 w-5 border-4 border-white border-t-transparent rounded-full" />
              : 'SIGN IN'}
          </button>

          {/* ----- Divider ----- */}
          {/* <div className="relative"> */}
          {/* <div className="absolute inset-0 flex items-center"> */}
          {/* <div className="w-full border-t border-white/10"></div> */}
          {/* </div> */}
          {/* <div className="relative flex justify-center text-sm"> */}
          {/* <span className="px-2 bg-gradient-to-b from-purple-900 to-indigo-900 text-white/60">
                Or continue with
              </span> */}
          {/* </div> */}
          {/* </div> */}

          {/* ----- Google Auth Button ----- */}
          {/* <button
            type="button"
            onClick={onGoogleAuth}
            className="w-full p-2 border border-white/20 rounded-lg flex items-center justify-center gap-2 text-white hover:bg-white/5"
          >
            Continue with Google
          </button> */}

          {/* ----- Forgot Password Link ----- */}


          {/* ----- Switch to Sign Up ----- */}
          <p className="text-center text-sm text-white/60">
            Don't have an account?{" "}
            <button
              type="button"
              className="text-white/90 hover:text-white "
              onClick={onSwitchTab}
            >
              Sign up
            </button>
          </p>
        </Form>
      )}
    </Formik>
  );
};

export default LoginForm;
