// src/context/CoinsContext.tsx
import React, { createContext, useContext, ReactNode, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { GET_USER_COINS } from '../api/auth';
import { useAuth } from '../auth/AuthContext';

interface CoinsContextType {
  coins: number;
  isLoading: boolean;
  error: Error | null;
  updateCoins: (newAmount: number) => void;
  addCoins: (amount: number) => void;
  subtractCoins: (amount: number) => void;
  refetchCoins: () => void;
}

const CoinsContext = createContext<CoinsContextType | undefined>(undefined);

export const CoinsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { accessToken } = useAuth();
  const queryClient = useQueryClient();

  const {
    data,
    isLoading,
    error,
    refetch: refetchCoins
  } = useQuery({
    queryKey: ['coins'],
    queryFn: () =>
      fetch(GET_USER_COINS, { 
        headers: { Authorization: `Bearer ${accessToken}` },
        cache: 'force-cache'
      }).then((res) => res.json()),
    staleTime: 30000, // 30 seconds
    gcTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
    refetchInterval: false,
    enabled: !!accessToken
  });

  const coins = data?.data?.point;

  const updateCoins = useCallback((newAmount: number) => {
    queryClient.setQueryData(['coins'], (oldData: any) => ({
      ...oldData,
      data: {
        ...oldData?.data,
        point: newAmount
      }
    }));
  }, [queryClient]);

  // Function to add coins
  const addCoins = (amount: number) => {
    queryClient.setQueryData(['coins'], (oldData: any) => ({
      ...oldData,
      data: {
        ...oldData?.data,
        point: (oldData?.data?.point || 0) + amount
      }
    }));
  };

  // Function to subtract coins
  const subtractCoins = (amount: number) => {
    queryClient.setQueryData(['coins'], (oldData: any) => ({
      ...oldData,
      data: {
        ...oldData?.data,
        point: Math.max(0, (oldData?.data?.point || 0) - amount)
      }
    }));
  };

  const value = {
    coins,
    isLoading,
    error,
    updateCoins,
    addCoins,
    subtractCoins,
    refetchCoins
  };

  return <CoinsContext.Provider value={value}>{children}</CoinsContext.Provider>;
};

export const useCoins = (): CoinsContextType => {
  const context = useContext(CoinsContext);
  if (context === undefined) {
    throw new Error('useCoins must be used within a CoinsProvider');
  }
  return context;
};
