import { useEffect } from "react";
import { useAuth } from "../auth/AuthContext";

declare global {
  interface Window {
    zE?: any;
    zESettings?: any;
  }
}

type UserProfileState = {
  userProfile: any;
  error: string | null;
  loading: boolean;
}

type UserProfileStateProps = { userProfileState: UserProfileState };

export default function ZendeskWidget({ userProfileState }: UserProfileStateProps) {
  const { accessToken } = useAuth();
  const { userProfile, loading, error } = userProfileState;

  useEffect(() => {
    if (loading || error || !userProfile) {
      console.debug("ZendeskWidget: waiting for profile…", { loading, error });
      return;
    }
    console.debug("ZendeskWidget: profile loaded", userProfile);

    // 1) inject custom CSS
    console.debug("ZendeskWidget: injecting CSS");
    const style = document.createElement("style");
    style.innerHTML = `
      [data-testid="launcher"] { border-radius:50%!important; }
      iframe[title="Messaging window"] { border-radius:12px!important; box-shadow:0 10px 40px rgba(0,0,0,0.2)!important; }
      @media(max-width:640px){ iframe[title="Messaging window"]{width:90%!important;height:70%!important;} }
      @media(min-width:641px) and (max-width:1024px){ iframe[title="Messaging window"]{width:450px!important;height:650px!important;} }
    `;
    document.head.appendChild(style);

    // 2) basic widget settings
    window.zESettings = {
      messenger: { conversationTags: ["web-widget"] },
    };

    const initZendesk = () => {
      if (!window.zE) {
        console.warn("ZendeskWidget: zE not ready");
        return;
      }

      // 3) identify the user in Messenger
      window.zE("messenger", "updateSettings", {
        user: {
          // send the name and your system's unique user ID as externalId
          name: userProfile.name,
          externalId: userProfile.id,
        },
      });
      console.debug("ZendeskWidget: user identified", {
        name: userProfile.name,
        externalId: userProfile.id,
      });

      // 4) log in as authenticated user
      console.debug("ZendeskWidget: calling loginUser with jwtCallback");
      window.zE(
        "messenger",
        "loginUser",
        (err: any) => {
          if (err) {
            console.error("ZendeskWidget: loginUser error", err);
          } else {
            console.debug("ZendeskWidget: loginUser succeeded");
          }
        }
      );

      // 5) prefill phone if available
      if (userProfile.phone_number) {
        console.debug("ZendeskWidget: setting phone field", userProfile.phone_number);
        window.zE("messenger:set", "contactForm", {
          fields: [{ id: "phone", value: userProfile.phone_number }],
        });
      }

      // 6) finally show the messenger
      console.debug("ZendeskWidget: showing messenger");
      window.zE("messenger", "show");
      console.log("✅ ZendeskWidget initialized");
    };

    if (!document.getElementById("ze-snippet")) {
      console.debug("ZendeskWidget: injecting snippet.js");
      const script = document.createElement("script");
      script.id = "ze-snippet";
      script.src = "https://static.zdassets.com/ekr/snippet.js?key=92959ea6-5ea6-48ce-9079-015c2410b765";
      script.async = true;
      script.onload = () => setTimeout(initZendesk, 500);
      script.onerror = () => console.error("ZendeskWidget: failed to load snippet.js");
      document.body.appendChild(script);
    } else {
      console.debug("ZendeskWidget: snippet already present, init soon");
      setTimeout(initZendesk, 200);
    }

    // cleanup CSS
    return () => {
      if (style.parentNode) {
        document.head.removeChild(style);
        console.debug("ZendeskWidget: cleaned up CSS");
      }
    };
  }, [accessToken, userProfile, loading, error]);

  return null;
}
