export const measurePerformance = () => {
  if (typeof window !== 'undefined' && 'performance' in window) {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        console.log('🚀 Performance Metrics:');
        console.log(`DOM Content Loaded: ${perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart}ms`);
        console.log(`Page Load: ${perfData.loadEventEnd - perfData.loadEventStart}ms`);
        console.log(`Total Load Time: ${perfData.loadEventEnd - perfData.fetchStart}ms`);
      }, 0);
    });
  }
};

// Call in main.tsx
measurePerformance();