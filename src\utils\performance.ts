// Enhanced performance monitoring with detailed metrics
export const measurePerformance = () => {
  if (typeof window !== 'undefined' && 'performance' in window) {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paintEntries = performance.getEntriesByType('paint');

        console.log('🚀 Performance Metrics:');
        console.log(`DNS Lookup: ${perfData.domainLookupEnd - perfData.domainLookupStart}ms`);
        console.log(`TCP Connection: ${perfData.connectEnd - perfData.connectStart}ms`);
        console.log(`Request/Response: ${perfData.responseEnd - perfData.requestStart}ms`);
        console.log(`DOM Content Loaded: ${perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart}ms`);
        console.log(`Page Load: ${perfData.loadEventEnd - perfData.loadEventStart}ms`);
        console.log(`Total Load Time: ${perfData.loadEventEnd - perfData.fetchStart}ms`);

        // Paint metrics
        paintEntries.forEach((entry) => {
          console.log(`${entry.name}: ${entry.startTime.toFixed(2)}ms`);
        });

        // Memory usage (if available)
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          console.log(`Memory Used: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
          console.log(`Memory Limit: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`);
        }
      }, 0);
    });
  }
};

// Preload critical resources
export const preloadCriticalResources = () => {
  const criticalImages = [
    '/src/assets/homePageImages/cryptokings_icon.avif',
    '/src/assets/homePageImages/thevault_icon.avif',
    '/src/assets/homePageImages/wof_icon.avif',
    '/src/assets/homePageImages/sc_icon.avif'
  ];

  criticalImages.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
};

// Optimize images with lazy loading
export const optimizeImages = () => {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        }
      });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }
};

// Bundle size analyzer
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('📦 Bundle Analysis Available at: npm run analyze');
  }
};

// Call in main.tsx
measurePerformance();
preloadCriticalResources();
optimizeImages();