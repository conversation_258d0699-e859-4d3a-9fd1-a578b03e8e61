import axios from './axiosInstance'
import {
ROLL_DA_DICE_BET_PLACE
} from './auth';

export interface HistoryData {
  page: number;
  per_page: number;
  // total_pages: number;
}
interface BetData {
    bet_amount: number,
    timestamp: Date,
    user_guess_end_point: number,
    user_guess_start_point: number,
}

export const PlaceRollDiceBet = async (data: BetData, token: string) => {
  const response = await axios.post(ROLL_DA_DICE_BET_PLACE, data, {
    headers: {
      Authorization: `Bear<PERSON> ${token}`,
    },
  });
  return response.data;
};
export const getGameHistory = async (data: HistoryData, token: string) => {
  const response = await axios.get(ROLL_DA_DICE_BET_PLACE, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: {
      page: data.page,
      perPage: data.per_page,
    },
  });
  return response.data;
};