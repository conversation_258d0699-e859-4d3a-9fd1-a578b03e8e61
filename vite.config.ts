import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'

export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Core React
          'react-vendor': ['react', 'react-dom'],
          
          // Routing
          'router': ['react-router-dom'],
          
          // UI Libraries
          'ui-libs': ['lucide-react', 'react-hot-toast'],
          
          // Data & State
          'data-libs': ['@tanstack/react-query', 'axios'],
          
          // Real-time
          'socket': ['socket.io-client'],
          
          // Game Libraries (heavy)
          'game-libs': ['konva', 'react-konva'],
          
          // Forms
          'form-libs': ['formik', 'yup'],
          
          // Animations
          'animation-libs': ['@react-spring/web']
        }
      }
    },
    minify: 'esbuild',
    target: 'esnext',
    sourcemap: false,
    cssCodeSplit: true,
    chunkSizeWarningLimit: 1000
  },
  server: {
    hmr: {
      overlay: false,
      port: 24678
    },
    host: true,
    port: 5173,
    // Enable faster file watching
    watch: {
      usePolling: false,
      interval: 100
    },
    // Enable faster startup
    warmup: {
      clientFiles: ['./src/main.tsx', './src/App.tsx']
    }
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      'axios',
      'react-hot-toast',
      'lucide-react',
      'formik',
      'yup'
    ],
    exclude: ['konva', 'react-konva'],
    // Force optimization on startup
    force: true
  },
  esbuild: {
    target: 'esnext'
  }
})
