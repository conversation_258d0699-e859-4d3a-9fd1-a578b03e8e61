/**
 * Wallet Component
 * A modal interface for viewing, converting, and reviewing airtime transactions and utilities.
 *
 * @component
 * @param {Object} props
 * @param {() => void} props.onClose - Callback to close the wallet modal
 * @param {{ coins: number; fpp: number; real_money: string }} props.balance - User's balance details
 */
import React, { useState, useEffect } from 'react';
import {
  Wallet as WalletIcon,
  History,
  ArrowRightLeft,
  Phone,
  X,
  CheckCircle,
  Loader,
  BanknoteIcon,
  Plus
} from 'lucide-react';
import { useAuth } from '../auth/AuthContext';
import { useInfiniteQuery, useMutation } from '@tanstack/react-query';
import { ClaimAirtimes, getActiveUtilities, getAirTimeTransactions } from '../api/airTimeService';
import { CustomToast } from '../utils/validations/customeToast';
import { useCoins } from '../hooks/useCoinsQuery';

interface WalletProps {
  /** Function to close the modal */
  onClose: () => void;
  /** User balances */
  balance: {
    coins: number;
    fpp: number;
    real_money: string;
    points: number;

  };
}
/**
 * Formats a numeric value into a localized string with commas.
 * @param {number | string} value - The value to format.
 * @returns {string} The formatted number string.
 */
const formatCoins = (value: number | string): string => Number(value).toLocaleString();
/**
 * Formats a date string or Date object to dd.mm.yyyy
 * @param {string | number | Date} date
 * @returns {string}
 */
const formatDateDDMMYYYY = (date: string | number | Date): string => {
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  return `${day}.${month}.${year}`;
};

const Wallet: React.FC<WalletProps> = ({ onClose }) => {
  /** Currently active tab: overview, history, or convert */
  const [activeTab, setActiveTab] = useState<'overview' | 'history' | 'convert'>('overview');
  /** Show the confirmation modal */
  const [showConfirmation, setShowConfirmation] = useState(false);
  /** Currently selected utility for conversion */
  const [selectedItem, setSelectedItem] = useState<any>(null);
  /** Show the success modal */
  const [showSuccess, setShowSuccess] = useState(false);
  /** State flags for loading all data */
  const [isLoadingAllTransactions, setIsLoadingAllTransactions] = useState(false);
  const [isLoadingAllUtilities, setIsLoadingAllUtilities] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const { accessToken } = useAuth();
  const { coins, refetchCoins, isLoading: isLoadingCoins } = useCoins();

  /** Points alias for coins */
  const points = coins;

  /**
   * Fetch paginated airtime transactions
   */
  const {
    data: transactionsData,
    fetchNextPage: fetchNextPageForTransaction,
    hasNextPage: hasNextPageForTransaction,
    isFetchingNextPage: isFetchingNextPageForTransaction,
    refetch: refetchForTransaction,
    isLoading: isLoadingTransactions
  } = useInfiniteQuery({
    queryKey: ['transactions'],
    queryFn: ({ pageParam = 1 }) => {
      if (!accessToken) throw new Error("Access token missing");
      return getAirTimeTransactions({ page: pageParam, per_page: 10 }, accessToken);
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      // Determine if more pages exist
      if (!lastPage?.data?.total_pages || allPages.length >= lastPage.data.total_pages) return undefined;
      return allPages.length + 1;
    },
    staleTime: 0,
    refetchOnWindowFocus: true,
  });


  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Trigger animation
    setTimeout(() => setIsAnimating(true), 50);

    // Ensure that balance has renewed with an actual value
    refetchCoins();

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  /**
   * Mutation to claim airtime conversion
   */
  const claimAirTime = useMutation({
    mutationFn: async (data: { local_id: string }) =>
      ClaimAirtimes({ airtime_local_id: data.local_id }, accessToken!),
    onSuccess: () => {
      setShowSuccess(true);
      setShowConfirmation(false);
      refetchCoins();
      refetchForTransaction();
      setTimeout(() => setShowSuccess(false), 1000);
    },
    onError: (error: any) => {
      CustomToast('error', error.message);
    }
  });

  /**
   * Fetch paginated airtime utilities
   */
  const {
    data: utilitiesData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    isLoading: isLoadingUtilities
  } = useInfiniteQuery({
    queryKey: ['utilities'],
    queryFn: ({ pageParam = 1 }) => {
      if (!accessToken) throw new Error("Access token missing");
      return getActiveUtilities({ page: pageParam, per_page: 10 }, accessToken);
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage?.data?.total_pages || allPages.length >= lastPage.data.total_pages) return undefined;
      return allPages.length + 1;
    },
    staleTime: 0,
    refetchOnWindowFocus: true,
  });

  /**
   * Initiate conversion confirmation
   * @param option - selected utility option
   */
  const handleConversion = (option: any) => {
    setSelectedItem(option);
    setShowConfirmation(true);
  };

  /** Load next page of utilities */
  const loadMoreUtilities = () => {
    if (hasNextPage && !isFetchingNextPage) fetchNextPage();
  };

  /** Load next page of transactions */
  const loadMoreTransactions = () => {
    if (hasNextPageForTransaction && !isFetchingNextPageForTransaction)
      fetchNextPageForTransaction();
  };

  /** Fetch all utilities at once */
  const loadAllUtilities = async () => {
    if (!hasNextPage || isLoadingAllUtilities) return;
    setIsLoadingAllUtilities(true);
    try {
      while (hasNextPage) await fetchNextPage();
    } catch {
      CustomToast('error', 'Failed to load all utilities');
    } finally {
      setIsLoadingAllUtilities(false);
    }
  };

  /** Fetch all transactions at once */
  const loadAllTransactions = async () => {
    if (!hasNextPageForTransaction || isLoadingAllTransactions) return;
    setIsLoadingAllTransactions(true);
    try {
      while (hasNextPageForTransaction) await fetchNextPageForTransaction();
    } catch {
      CustomToast('error', 'Failed to load all transactions');
    } finally {
      setIsLoadingAllTransactions(false);
    }
  };

  // Flattened lists of all pages
  const allTransactions = transactionsData?.pages?.flatMap(p => p.data?.transactions || []) || [];
  const allUtilities = utilitiesData?.pages?.flatMap(p => p.data?.airtime_utilities || []) || [];

  // Quick-convert first four utilities
  interface UtilityOption {
    id: string;
    billerName: string;
    price: number;
    amount?: number

  }
  // Mobile bottom sheet styles
  const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const mobileContentClass = `
  bg-gradient-to-b from-[#510957] to-black
    rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out
    ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;

  // Desktop modal styles
  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const desktopContentClass = `
   bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
  rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col
  transform transition-all duration-300 ease-out
  ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
`;


  const quickConvertOptions: UtilityOption[] = utilitiesData?.pages?.[0]?.data?.airtime_utilities?.slice(0, 4) || [];

  /** Loading spinner for lists */
  const LoadingSpinner = () => (
    <div className="flex justify-center items-center py-8">
      <Loader size={24} className="text-yellow-400 animate-spin" />
    </div>
  );
  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(onClose, 300);
  };

  return (
    <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
      <div className={isMobile ? mobileContentClass : desktopContentClass}>

        {/* Header */}
        <div className="p-6  flex justify-between items-center sticky top-0  from-[#510957] to-[#510957]rounded-t-3xl md:rounded-t-2xl z-10">
          <h2 className="text-xl  text-white font-[Anton] tracking-wide">Wallet</h2>
          <button
            onClick={handleClose}
            className="text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-full"
          >
            <X size={20} />
          </button>
        </div>


        {/* Mobile handle bar */}
        {isMobile && (
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-1 bg-white/30 rounded-full"></div>
          </div>
        )}



        {/* Toggle Tabs for login/signup only */}

        <nav className="flex bg-white/5 p-1 rounded-full mx-4 mt-4">
          {[
            { key: 'overview', label: 'OVERVIEW' },
            { key: 'history', label: 'HISTORY' },
            { key: 'convert', label: 'CONVERT' }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`
                     flex-1 py-2 px-4
                     rounded-xl
                     text-[20px]
                     tracking-wider
                     font-normal uppercase font-[Anton]
                     transition-all duration-200
                     ${activeTab === tab?.key
                  ? 'bg-white text-black'
                  : 'text-white/40'}
                   `}
            >
              {tab.label}
            </button>
          ))}
        </nav>


        {/* Set a fixed height for the tab content container */}



        <div className="flex-1 overflow-y-auto">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="p-6 space-y-6">
              <div className="bg-white/10 rounded-xl p-6">
                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-[#1c1c1c] p-3 rounded-lg">
                    <BanknoteIcon
                      size={16}
                      className="text-accent-gold w-4 h-4 sm:w-5 sm:h-5"
                    />
                  </div>
                  <div>
                    <p className="text-sm text-white font-[Anton]">Bucks</p>
                    <p className="text-2xl text-white ">
                      {formatCoins(Number(points) > 0 ? Number(points).toFixed(0) : "0")}
                    </p>

                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={() => setActiveTab('history')}
                    className="flex items-center gap-2 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
                  >
                    <History size={18} className="text-white/60" />
                    <span className="text-sm font-medium">View History</span>
                  </button>
                  <button
                    onClick={() => setActiveTab('convert')}
                    className="flex items-center gap-2 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
                  >
                    <ArrowRightLeft size={18} className="text-white/60" />
                    <span className="text-sm font-medium">Convert Bucks</span>
                  </button>
                </div>
              </div>

              <div>
                {/* quickConvertOptions.map((option: UtilityOption) => ( */}
                {isLoadingUtilities ? (
                  <LoadingSpinner />
                ) : (
                  <div className="grid grid-cols-2 gap-4">
                    {quickConvertOptions.length > 0 ?
                      quickConvertOptions.map(option => (
                        <button
                          key={option.id}
                          onClick={() => handleConversion(option)}
                          className="bg-white/5 p-4 rounded-xl hover:bg-white/10 transition-colors text-left w-full"
                        >
                          <p className="font-normal text-[14px] tracking-wider font-[Anton]">
                            {option?.billerName}
                          </p>
                          <p className="text-sm text-white/60">
                            From {option?.price} Bucks
                          </p>
                          <p className="text-sm text-white/60">
                           Amount: ₦{option?.amount}
                          </p>
                        </button>

                      )) : (
                        <p className="text-center text-white/60 col-span-2">No Utilities found.</p>
                      )
                    }
                  </div>
                )}
              </div>
            </div>
          )}

          {/* History Tab */}
          {activeTab === 'history' && (
            <div className="p-6">
              {isLoadingTransactions ? (
                <LoadingSpinner />
              ) : (
                <div className="space-y-4">
                  {allTransactions.length > 0 ? (
                    <>
                      {allTransactions.map(transaction => (
                        <div
                          key={transaction.id}
                          className="w-full bg-[#131313] p-4 rounded-xl flex items-center justify-between transition-colors disabled:opacity-50"
                        >
                          <div className="flex items-center gap-4">
                            {/* Icon Container */}
                            <div className="bg-[#1c1c1c] p-2 rounded-lg">
                              <div className="bg-[#ED0CFF] p-1 rounded-lg flex items-center justify-center">
                                <Plus size={20} className="text-black font-bold" />
                              </div>
                            </div>

                            {/* Transaction Details */}
                            <div className="space-y-3">
                              <p className="text-[14px] text-white font-[Anton] leading-tight  tracking-wider ">
                                {transaction?.packageName}
                              </p>
                              {/* <p className="text-sm text-white/60 leading-none">{transaction?.type || 'Postpaid'}</p> */}
                              <p className="text-sm text-white/60 leading-none">
                                {formatDateDDMMYYYY(transaction.timestamp)}
                              </p>
                            </div>
                          </div>

                          {/* Amount */}
                          <div className="text-right text-[14px] text-white text-base font-[Anton]">
                            <span
                              className={`'text-red-400'
                                }`}
                            >
                              {/* Format: + $10.00 or - $10.00 */}
                              {transaction.status === 'success' ? '+ ' : '- '}
                              ${Number(transaction.cashout).toFixed(2)}
                            </span>
                          </div>
                        </div>

                      ))}

                      {/* {hasNextPageForTransaction && ( */}
                      <div className="text-center pt-4 flex gap-2 justify-center">
                        <button
                          onClick={loadMoreTransactions}
                          className="px-4 py-2 bg-white/10 rounded-lg  font-[Anton] hover:bg-white/20 transition-colors text-sm"
                          disabled={isFetchingNextPageForTransaction || isLoadingAllTransactions}
                        >
                          {isFetchingNextPageForTransaction ? (
                            <div className="flex items-center justify-center gap-2 ">
                              <Loader size={16} className="animate-spin" />
                              <span>Loading more...</span>
                            </div>
                          ) : (
                            "Load More"
                          )}
                        </button>

                        {/* <button
                          onClick={loadAllTransactions}
                          className="px-4 py-2 bg-white text-black  font-[Anton] rounded-lg  transition-colors text-sm"
                          disabled={isFetchingNextPageForTransaction || isLoadingAllTransactions || !hasNextPageForTransaction}
                        >
                          {isLoadingAllTransactions ? (
                            <div className="flex items-center justify-center gap-2">
                              <Loader size={16} className="animate-spin" />
                              <span>Loading all...</span>
                            </div>
                          ) : (
                            "Load All"
                          )}
                        </button> */}
                      </div>
                      {/* )} */}
                    </>
                  ) : (
                    <p className="text-center text-white/60">No transactions found.</p>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Convert Tab */}
          {activeTab === 'convert' && (
            <div className="p-6">
              {isLoadingUtilities ? (
                <LoadingSpinner />
              ) : (
                <div className="space-y-4">
                  {allUtilities.length > 0 ? (
                    <>
                      {allUtilities.map(option => (
                        <button
                          key={option.id}
                          onClick={() => handleConversion(option)}
                          className="w-full bg-[#131313] p-4 rounded-xl flex items-center justify-between transition-colors disabled:opacity-50"
                          disabled={claimAirTime.isPending}
                        >
                          {/* Left side: icon + name/subtitle */}
                          <div className="flex items-center gap-4">
                            <div className="bg-white/10 p-3 rounded-lg">
                              <Phone size={24} className="text-[#ED0CFF]" />
                            </div>
                            <div className="text-left">
                              <p className="font-[Anton] tracking-wider text-white text-[14px] uppercase">
                                {option.billerName}
                              </p>
                              {/* <p className="text-sm text-white/60">Prepaid</p> */}
                            </div>
                          </div>

                          {/* Right side: price */}
                          <span className="font-[Anton] text-white text-lg uppercase">
                            From ${option.price}
                          </span>
                        </button>

                      ))}

                      {hasNextPage && (
                        <div className="text-center pt-4 flex gap-2 justify-center">
                          <button
                            onClick={loadMoreUtilities}
                            className="px-4 py-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors text-sm"
                            disabled={isFetchingNextPage || isLoadingAllUtilities}
                          >
                            {isFetchingNextPage ? (
                              <div className="flex items-center justify-center gap-2">
                                <Loader size={16} className="animate-spin" />
                                <span>Loading more...</span>
                              </div>
                            ) : (
                              "Load More"
                            )}
                          </button>

                          {/* <button
                            onClick={loadAllUtilities}
                            className="px-4 py-2 bg-white text-black rounded-lg  transition-colors text-sm"
                            disabled={isFetchingNextPage || isLoadingAllUtilities || !hasNextPage}
                          >
                            {isLoadingAllUtilities ? (
                              <div className="flex items-center justify-center gap-2">
                                <Loader size={16} className="animate-spin" />
                                <span>Loading all...</span>
                              </div>
                            ) : (
                              "Load All"
                            )}
                          </button> */}
                        </div>
                      )}
                    </>
                  ) : (
                    <p className="text-center text-white/60">No Utilities found.</p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Confirmation Modal */}
        {showConfirmation && selectedItem && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-gradient-to-b from-[#4f0955] to-black rounded-2xl p-6 w-full max-w-sm text-white font-[Anton]">
              <h3 className="text-2xl font-bold mb-6 text-center">Confirm Conversion</h3>

              <div className="bg-[#131313] rounded-xl p-5 mb-6 space-y-2">
                <p className="text-lg tracking-wide">{selectedItem.billerName}</p>
                <div className="text-sm text-white/80">
                  <p>Cost: <span className="font-semibold text-white">{selectedItem.price} Bucks</span></p>
                  <p>Amount:<span className="font-semibold text-white"> ₦ {selectedItem.amount}</span></p>
                </div>
              </div>

              <div className="flex gap-4">
                <button
                  onClick={() => setShowConfirmation(false)}
                  className="flex-1 py-2.5 rounded-lg bg-white/10 hover:bg-white/20 transition-colors text-sm"
                  disabled={claimAirTime.isPending}
                >
                  Cancel
                </button>

                <button
                  onClick={() => claimAirTime.mutate({ local_id: selectedItem?.local_id })}
                  className="flex-1 py-2.5 rounded-lg bg-white text-black text-sm font-semibold hover:bg-gray-200 transition-colors"
                  disabled={claimAirTime.isPending}
                >
                  {claimAirTime.isPending ? (
                    <div className="flex items-center justify-center gap-2">
                      <Loader size={16} className="animate-spin" />
                      <span>Processing...</span>
                    </div>
                  ) : (
                    "Confirm"
                  )}
                </button>
              </div>
            </div>
          </div>

        )}


        {/* Success Modal */}
        {showSuccess && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl p-6 text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle size={32} className="text-green-400" />
              </div>
              <h3 className="text-xl font-bold mb-2">Conversion Successful!</h3>
              <p className="text-white/60">
                You've successfully converted {selectedItem?.price} Bucks to {selectedItem?.billerName}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Wallet;
