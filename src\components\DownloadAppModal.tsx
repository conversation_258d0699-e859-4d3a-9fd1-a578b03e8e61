import { useState, useEffect, FC } from 'react';
import { IMAGES } from '../constant/image';
import { PzButton } from './Shared/PzButton';

interface DownloadAppDialogProps {
  open: boolean;
  onClose: () => void;
  downloadUrl?: string;
}

const DownloadAppDialog: FC<DownloadAppDialogProps> = ({
  open,
  onClose,
  downloadUrl = import.meta.env?.VITE_APP_DOWNLOAD_URL,
}) => {
  const [isMobile, setIsMobile] = useState<boolean>(
    typeof window !== 'undefined' && window.innerWidth < 640
  );

  useEffect(() => {
    const onResize = (): void => setIsMobile(window.innerWidth < 640);
    window.addEventListener('resize', onResize);
    return () => window.removeEventListener('resize', onResize);
  }, []);


  if (!open) return null;

  const handleDownload = () => {
    window.open(downloadUrl, '_blank', 'noopener');
  };

  const containerClass = isMobile
    ? 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center'
    : 'fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4';

  const contentClass = isMobile
    ? 'bg-black/90 w-full max-h-[85vh] flex flex-col'
    : 'bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl w-full max-w-md max-h-[85vh] flex flex-col';

  return (
    <div className={containerClass}>
      <div 
        className={`${contentClass} relative overflow-hidden`}
        style={{
            backgroundImage: `url(${IMAGES.ECLIPSE_BG})`,
            backgroundSize: 'cover',
            backgroundColor: '#0E090E',
        }}
      >
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-2 right-3 text-white text-2xl p-0"
          aria-label="Close"
        >
          &times;
        </button>

        {/* Content area */}
        <div className="flex-1 flex flex-col md:flex-row items-center md:items-start p-6 pt-6 gap-4">
         <div className="flex-shrink-0 order-1 md:order-2">
            <img
              src={IMAGES.APP_DOWNLOAD}
              alt="Download the app illustration"
              className="w-40 h-auto"
            />
          </div>
          <div className="text-white flex-1 order-2 md:order-1">
            <h2 
                className={'text-white text-4xl mb-4 text-center'}
                style={{
                  fontFamily: 'Bebas Neue',
                }}
            >Download the App</h2> 
            <p className="mb-4">
              Enjoy a seamless experience by downloading our mobile app.
            </p>
            <PzButton
                text="Get the app"
                onClick={handleDownload}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadAppDialog;
