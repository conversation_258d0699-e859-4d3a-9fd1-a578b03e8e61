# 🎮 Rise and Hustle

**Rise and Hustle** is a modern, interactive gaming platform built with React and TypeScript. It features multiple casino-style games, real-time multiplayer functionality, and a comprehensive reward system designed to provide an engaging gaming experience.

## 🌟 Overview

Rise and Hustle combines the excitement of casino gaming with modern web technologies to deliver a seamless, responsive gaming platform. Players can enjoy various games, earn virtual currency, participate in tournaments, and climb leaderboards while enjoying a polished user interface.

## 🚀 Features

### 🎯 Core Gaming Features
- **Multiple Game Types**: Crash games, Plinko, Higher/Lower, Roll Dice, Crypto King, and more
- **Real-time Multiplayer**: Live gaming sessions with Socket.io integration
- **Virtual Currency System**: Coins and FPP (Frequent Player Points) economy
- **Progressive Rewards**: Daily rewards, achievements, and bonus systems
- **Interactive Elements**: Scratch cards, Wheel of Fortune, and boost modals

### 🔐 User Management
- **Secure Authentication**: JWT-based authentication with Google OAuth integration
- **User Profiles**: Comprehensive user management with XP and level systems
- **Protected Routes**: Role-based access control for authenticated features
- **Password Recovery**: OTP-based password reset functionality

### 🎨 User Experience
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Progressive Web App**: Optimized for mobile devices with app-like experience
- **Real-time Notifications**: Toast notifications and live updates
- **Smooth Animations**: React Spring animations for enhanced UX
- **Dark Theme**: Modern dark UI design

### 🏆 Social Features
- **Leaderboards**: Global and game-specific rankings
- **Squads System**: Team-based gameplay and competitions
- **Social Sharing**: Share achievements and invite friends
- **Live Chat**: Real-time communication between players

## 🛠️ Tech Stack

### Frontend Framework
- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and development server

### Styling & UI
- **Tailwind CSS** - Utility-first CSS framework
- **Styled Components** - CSS-in-JS styling
- **Lucide React** - Beautiful icon library
- **React Spring** - Physics-based animations

### State Management & Data
- **TanStack Query (React Query)** - Server state management
- **React Context** - Global state management
- **Formik & Yup** - Form handling and validation

### Networking & Real-time
- **Axios** - HTTP client for API requests
- **Socket.io Client** - Real-time bidirectional communication
- **React Router DOM** - Client-side routing

### Gaming & Graphics
- **Konva & React Konva** - 2D canvas library for game graphics
- **Embla Carousel** - Touch-friendly carousel component

### Development Tools
- **ESLint** - Code linting and quality
- **PostCSS** - CSS processing
- **Day.js** - Lightweight date manipulation

## 📁 Project Structure

```
rise_and_hustle/
├── public/                 # Static assets
├── src/
│   ├── api/               # API services and endpoints
│   ├── assets/            # Images, fonts, and static resources
│   ├── auth/              # Authentication context and utilities
│   ├── components/        # Reusable UI components
│   │   ├── Layout.tsx     # Main layout wrapper
│   │   ├── ProtectedRoute.tsx
│   │   ├── ScratchCard.tsx
│   │   ├── WheelOfFortune.tsx
│   │   └── ...
│   ├── context/           # React context providers
│   ├── hooks/             # Custom React hooks
│   ├── pages/             # Page components
│   │   ├── Home.tsx       # Landing page
│   │   ├── CrashGame.tsx  # Crash game implementation
│   │   ├── PlinkoGame.tsx # Plinko game
│   │   ├── Sportsbook.tsx # Sports betting
│   │   └── ...
│   ├── styles/            # Global styles and themes
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions and helpers
│   ├── App.tsx            # Main application component
│   └── main.tsx           # Application entry point
├── dist/                  # Production build output
├── package.json           # Dependencies and scripts
├── vite.config.ts         # Vite configuration
├── tailwind.config.js     # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
└── vercel.json            # Vercel deployment configuration
```

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v18 or higher)
- **npm** or **yarn** package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd rise_and_hustle
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```
   The application will be available at `http://localhost:5173`

### Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server with hot reload |
| `npm run build` | Build for production |
| `npm run preview` | Preview production build locally |
| `npm run lint` | Run ESLint for code quality checks |

## 🎮 Game Features

### 🎲 Available Games

1. **Street King (Crash Game)** - Multiplayer crash game with real-time betting
2. **Street King 2** - Enhanced version of the crash game
3. **Crypto King** - Cryptocurrency-themed trading game
4. **Quick Hustle** - Higher/Lower prediction game
5. **Roll Dice** - Classic dice rolling with multiple betting options
6. **Plinko** - Physics-based ball drop game
7. **Sportsbook** - Sports betting platform

### 🎁 Bonus Features

- **Scratch Cards** - Instant win lottery tickets
- **Wheel of Fortune** - Spin-to-win bonus rounds
- **Daily Rewards** - Login bonuses and streaks
- **Lottery System** - Periodic lottery draws
- **Boost Modals** - Temporary game enhancements

## 🏗️ Architecture

### Performance Optimizations
- **Code Splitting** - Lazy loading for optimal bundle sizes
- **Resource Preloading** - Critical resource preloading
- **Manual Chunking** - Vendor and feature-based code splitting
- **Terser Minification** - Production code optimization

### Real-time Features
- **Socket.io Integration** - Live game updates and chat
- **Real-time Balance Updates** - Instant coin and FPP updates
- **Live Leaderboards** - Dynamic ranking updates
- **Multiplayer Support** - Concurrent gaming sessions

### Security
- **JWT Authentication** - Secure token-based authentication
- **Protected Routes** - Route-level access control
- **Input Validation** - Formik and Yup validation
- **HTTPS Enforcement** - Secure data transmission

## 🌐 Deployment

### Vercel Deployment
The project is configured for Vercel deployment with optimized caching headers:

```json
{
  "headers": [
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

### Build Configuration
- **Vite Build Tool** - Fast builds with optimized output
- **CSS Code Splitting** - Separate CSS bundles for better caching
- **Asset Optimization** - Automatic image and font optimization
- **Progressive Web App** - Service worker for offline functionality

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Use ESLint configuration for code consistency
- Write meaningful commit messages
- Test your changes thoroughly
- Update documentation as needed

## 📱 Mobile Support

- **Responsive Design** - Optimized for all screen sizes
- **Touch Gestures** - Native touch interactions
- **PWA Features** - App-like experience on mobile
- **Android Detection** - Platform-specific optimizations
- **Download Prompts** - Native app installation prompts

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:

```env
VITE_API_BASE_URL=your_api_base_url
VITE_SOCKET_URL=your_socket_server_url
VITE_GOOGLE_CLIENT_ID=your_google_oauth_client_id
```

### Tailwind Configuration
The project uses a custom Tailwind configuration with:
- Custom color palette
- Extended spacing scale
- Custom animations
- Mobile-first breakpoints

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- React team for the amazing framework
- Vite team for the blazing fast build tool
- Tailwind CSS for the utility-first approach
- Socket.io for real-time capabilities
- All contributors and the open-source community

---

**Built with ❤️ by the Rise and Hustle team**
