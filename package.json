{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --port 5173", "dev:turbo": "vite --force --host --port 5173", "dev:debug": "vite --host --port 5173 --debug", "build": "vite build", "build:fast": "vite build --minify esbuild", "preview": "vite preview --host --port 4173", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "lint": "eslint .", "optimize": "vite optimize"}, "dependencies": {"@react-spring/web": "^9.7.5", "@tanstack/react-query": "^5.74.3", "axios": "^1.8.4", "dayjs": "^1.11.13", "embla-carousel-react": "^8.0.0", "formik": "^2.4.6", "konva": "^8.4.3", "lucide-react": "^0.358.0", "react": "^18.3.1", "react-data-table-component": "^7.7.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-konva": "^18.2.10", "react-router-dom": "^6.22.3", "react-toastify": "^11.0.5", "react-zendesk": "^0.1.13", "socket.io-client": "^4.8.1", "use-image": "^1.1.4", "workbox-webpack-plugin": "^7.3.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.15.24", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitejs/plugin-react-swc": "^3.11.0", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-bundle-analyzer": "^1.1.0"}}