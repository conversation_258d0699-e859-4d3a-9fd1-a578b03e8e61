import React, { Suspense, useState } from 'react';
import { useAuth } from '../auth/AuthContext';
import LoginPromptPopup from '../components/LoginPromptPopup';
import { useNavigate } from 'react-router-dom';
import { lazyWithPreload } from '../utils/lazyWithPreload';
import StoryCarousel from '../components/StoryCarousel';
import { IMAGES } from '../constant/image';

// Lazy load components for better performance
const VideoAd = lazyWithPreload(() => import('../components/VideoAd'));
const BoostModal = lazyWithPreload(() => import('../components/BoostModal'));

/**
 * Interface for Home component props
 * @interface HomeProps
 * @property {function} setShowScratchCard - Function to toggle scratch card visibility
 * @property {function} setShowWheel - Function to toggle wheel visibility
 */
interface HomeProps {
  setShowScratchCard: (arg: boolean) => void;
  setShowWheel: (arg: boolean) => void;
}

/**
 * Home component - Main landing page of the application
 * @component
 * @param {HomeProps} props - Component props
 * @returns {React.ReactElement} Rendered component
 */
const Home: React.FC<HomeProps> = ({ setShowScratchCard, setShowWheel }) => {
  // Hooks and state initialization
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showVideoAd, setShowVideoAd] = useState(false);
  const [showBoostModal, setShowBoostModal] = useState(false);

  /**
   * Shows login prompt when user is not authenticated
   * @param {React.MouseEvent} [e] - Optional mouse event
   * @returns {void}
   */
  const handleRequireLogin = (e?: React.MouseEvent) => {
    if (e) e.preventDefault();
    setShowLoginPrompt(true);
  };

  /**
   * Navigates to Street King game if authenticated
   * @returns {void}
   */
  const handlePlayStreetHustle = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate('/street-king');
  };

  /**
   * Navigates to Street King 2 game if authenticated
   * @returns {void}
   */
  const handlePlayStreetHustle2 = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate('/street-king-2');
  };

  /**
   * Navigates to Crypto King game if authenticated
   * @returns {void}
   */
  const handlePlayCryptoKing = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate('/crypto-king');
  };

  /**
   * Navigates to Quick Hustle game if authenticated
   * @returns {void}
   */
  const handlePlayQuickHustle = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate('/quick-hustle');
  };

  /**
   * Navigates to Roll Dice game if authenticated
   * @returns {void}
   */
  const handlePlayRollDice = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate('/roll-dice');
  };

  /**
   * Navigates to Plinko game if authenticated
   * @returns {void}
   */
  const handlePlinkoGame = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate('/plinko');
  };

  /**
   * Navigates to Sportsbook if authenticated
   * @returns {void}
   */
  const handleSportsbook = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate("/sportsbook");
  };

  /**
   * Handles sharing to social platforms
   * @param {string} platform - Platform to share to
   * @returns {void}
   */
  const handleShare = (platform: string) => {
    // Implement share functionality
    console.log(`Sharing to ${platform}`);
    setShowBoostModal(false);
  };

  return (
    <div className="pb-3">
      {/* Story Carousel */}
      <StoryCarousel />

      {/* Quick Games */}

      <div className="bg-black">
        <h3 className="text-[20px]  leading-6 font-normal font-[Anton] text-white mb-5">
          Quick games
        </h3>

        <div className="grid grid-cols-2 gap-4 mt-8">
          {/* Scratch Card */}
          <div
            onClick={() => {
              if (!isAuthenticated) return handleRequireLogin();
              setShowScratchCard(true);
            }}
            className="relative
       rounded-[24px]
        bg-[linear-gradient(45deg,_#AE11BB,_#000000)]
        shadow-md 
        transform transition-transform duration-300 
        hover:scale-[1.02]
        cursor-pointer
        flex flex-col justify-between
        min-h-[220px]
        z-10
  "
          >

            {/* Game Image - Updated positioning */}
            <div className="absolute top-[-50px] right-[-40px]  z-999">
              <img
                src={IMAGES.SCRATCH_CARD}
                alt="Scratch Card"
                className="w-[200px] h-auto object-contain drop-shadow-lg"
                sizes="190px"
              />
            </div>
            <div className="absolute inset-0 z-10 flex flex-col items-start justify-end px-6 pb-4">
              <h4 className="text-lg font-semibold font-[Anton] text-white">
                Scratch Card
              </h4>
              <p className="text-gray-200 text-xs sm:text-sm leading-snug">
              Instant wins of airtime & crypto
              </p>
            </div>

          </div>

          {/* Wheel of Fortune */}
          <div
            onClick={() => {
              if (!isAuthenticated) return handleRequireLogin();
              setShowWheel(true);
            }}
            className="relative
       rounded-[24px]
        bg-[linear-gradient(45deg,_#AE11BB,_#000000)]
        shadow-md 
        transform transition-transform duration-300 
        hover:scale-[1.02]
        cursor-pointer
        flex flex-col justify-between
        min-h-[220px]
        z-10
  "
          >
            {/* Game Image - Updated positioning */}
            <div className="absolute top-[-30px] right-[-20px]  z-10">
              <img
                src={IMAGES.SPINNING_WHEEL}
                alt="Wheel of Luck"
                className="w-[150px] h-auto object-cover drop-shadow-lg"
              />
            </div>
            <div className="absolute inset-0 z-10 flex flex-col items-start justify-end px-6 pb-4">
              <h4 className="text-lg font-semibold font-[Anton] text-white">
                Wheel of Luck
              </h4>
              <p className="text-gray-200 text-xs sm:text-sm leading-snug">
            Spin to win big cash & rewards
              </p>
            </div>

          </div>
        </div>
      </div>

      <h3 className="text-[20px] leading-6 font-normal font-[Anton] text-white mt-6">
        Featured Games
      </h3>
      <div className="space-y-4 mt-3">
        <div className="grid grid-cols-2 gap-4 mt-14">
          {/* Crypto King */}
          <div
            onClick={handlePlayCryptoKing}
            className="relative
       rounded-[24px]
        bg-[linear-gradient(to_bottom,_#AE11BB,_#000000)]
        shadow-md 
        transform transition-transform duration-300 
        hover:scale-[1.02]
        cursor-pointer
        flex flex-col justify-between
        min-h-[220px]
        z-10
  "
          >


            <img
              src={IMAGES.CRYPTO_KING}
              alt="Crypto King"
            className="absolute md:w-[190px] md:max-w-[190px] sm:w-full sm:max-w-[110px] h-auto drop-shadow-2xl bottom-0 md:left-[10px] left-[0px]"

            />
            {/* Floating image */}
            <div className="absolute right-0 left-0 top-0 w-full z-10 bottom-0 flex justify-left items-end  bg-[linear-gradient(359deg,_rgba(0,0,0,0.6)_29%,_transparent_75%)]">
              <h4 className="text-lg ps-6 font-semibold font-[Anton] text-white text-center pb-4">
                Crypto King
              </h4>
            </div>


            <div className="relative flex flex-col mt-3 items-center justify-end h-full w-full z-30 mt-auto">
              {/* <h4 className="text-lg font-semibold font-[Anton] text-white text-center pb-4">
                Crypto King
              </h4> */}
            </div>
          </div>

          {/* Lootbox */}
          <div
            onClick={() => {
              if (!isAuthenticated) return handleRequireLogin();
              // Add lootbox functionality here
              handlePlayRollDice();
            }}
            className="relative
       rounded-[24px]
        bg-[linear-gradient(to_bottom,_#AE11BB,_#000000)]
        shadow-md 
        transform transition-transform duration-300 
        hover:scale-[1.02]
        cursor-pointer
        flex flex-col justify-between
        min-h-[220px]
        z-10
  "
          >
            <div
              className="
             absolute top-0 left-0
       w-full h-full
 rounded-[2px] bg-[linear-gradient(359deg,#000000f5,transparent)]

      z-[12]
    bg-cover bg-center
  "
            />
            <img
              src={IMAGES.LOOTBOX}
              alt="Crypto King"
              className="absolute w-[155px] max-w-[145px] h-auto  drop-shadow-2xl bottom-0 right-0 "
            // sizes="(max-width: 768px) 120vw, 50vw"
            />
            {/* Floating image */}
            <div className="absolute right-0 left-0 top-0 w-full z-10 bottom-0 flex justify-left items-end z-20 ">
              <h4 className="text-lg ps-6 font-semibold font-[Anton] text-white text-center pb-4">
                   Dice
              </h4>
            </div>

            <div className="relative flex flex-col mt-3 items-center justify-end h-full w-full z-30 mt-auto">

            </div>
          </div>
        </div>
      </div>

      {/* Boost Section */}
      <div className=" rounded-xl">
        <div className="flex items-center justify-between mb-3 mt-8">
          <div>
            <h3 className="text-[20px] leading-6 font-normal font-[Anton] mb-1">
              Boost Your Progress
            </h3>
            <p className="text-white/60 font-poppins text-sm leading-5 font-normal">
              Watch ads or share to earn Bucks
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mt-10">
          <button
            onClick={() => {
              if (!isAuthenticated) return handleRequireLogin();
              setShowVideoAd(true);
            }}
            className="
               relative 
    bg-gradient-to-b from-[#4f1154]  via-[#4f1154]  to-[#4f1154] 
    rounded-2xl pt-16 pb-6 px-6 
    flex flex-col items-center justify-center 
    transform transition-all duration-300 hover:scale-[1.02]
    shadow-lg
  "
          >
            <div
              className="
    absolute top-0 left-0
    w-full h-full
    bg-[linear-gradient(189deg,#000000c7,transparent)]
    z-[12]
    bg-cover bg-center
  "
            />

            {/* floated, rotated icon */}
            <div
              className="
              
    absolute 
    -top-8
    w-36 h-36 
    flex items-center justify-center
    z-30
  "
            >
              <img
                src={IMAGES.WATCH_ICON}
                alt="Watch Icon"
                loading="lazy"
                className="
        w-[217px] h-[175px]
        object-cover 
        rounded-xl 
        transform rotate-[-1deg]
        drop-shadow-2xl
      "
              />
            </div>

            {/* push content down to make room for the icon */}
            <p className="mt-10 font-normal font-[Anton] z-30">Watch Video</p>
            <p className="mt-2 text-gray-300 text-base z-30">+50 Bucks</p>
          </button>

          <button
            onClick={() => {
              if (!isAuthenticated) return handleRequireLogin();
              setShowBoostModal(true);
            }}
            className="
            relative 
   bg-gradient-to-b from-[#4f1154]  via-[#4f1154]  to-[#4f1154] 
  rounded-2xl pt-16 pb-6 px-6 
  flex flex-col items-center justify-center 
  transform transition-all duration-300 hover:scale-[1.02]
  shadow-lg
            "
          >
            <div
              className="
    absolute top-0 left-0
    w-full h-full
    bg-[linear-gradient(189deg,#000000c7,transparent)]
    z-[12]
    bg-cover bg-center
  "
            />
            {/* floated, rotated icon */}
            <div
              className="
              absolute 
              -top-8
              w-38 h-36 
              rounded-full 
              bg-transparent 
              flex items-center justify-center
              z-30
            "
            >
              <img
                src={IMAGES.MOREWAYS_ICON}
                alt={'More Way Icon'}
                loading="lazy"
                className="
                  w-[217px] h-[175px]
                  object-cover 
                  rounded-xl 
                  transform 
                   rotate-[-1deg]

                  drop-shadow-2xl
                "
              />
            </div>

            {/* push content down to make room for the icon */}
            <h3 className="mt-10 font-normal font-[Anton] z-30">
              {'More Ways'}
            </h3>
            <p className="mt-2 text-gray-300 text-base z-30">
              {'Up to +100 Bucks'}
            </p>
          </button>
        </div>
      </div>

      {/* More Games */}
      <div className="space-y-4 mt-3">
        <h3 className="text-[20px] leading-6 font-normal font-[Anton]">
          More ways to chop
        </h3>

        {/* ─── Roll da Dice Card ─── */}
        {/* <div
          onClick={handlePlayRollDice}
          className="relative rounded-xl overflow-hidden bg-[#131313] cursor-pointer shadow-sm transform transition-all duration-300 hover:scale-[1.02]"
        >
          <div className="absolute inset-0 bg-black/40 border border-[#ED0CFF]  rounded-xl"></div>
          <div className="relative z-10 flex items-center gap-4 p-4">
            <div className="bg-white/10  rounded-lg flex items-center justify-center w-16 h-16">
              <img
                src={IMAGES.BIT_COINS}
                alt="Crypto King"
                loading="lazy"
                className="w-full h-full object-cover  rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[14px] font-normal leading-5 tracking-[−0.04em] font-[Anton]">
                Dice
              </h4>
              <p className="text-[12px] text-white/80">
                Test your luck, roll to win
              </p>
            </div>
          </div>
        </div> */}

        {/* ─── Plinko Card ─── */}
        <div
          className="relative rounded-xl overflow-hidden  bg-[#131313] cursor-pointer shadow-sm transform transition-all duration-300 hover:scale-[1.02]"
        >
          <div className="absolute inset-0 bg-black/40 border border-[#ED0CFF]  rounded-xl"></div>
          <div className="relative z-10 flex items-center gap-4 p-4">
            <div className="bg-[#29122b]  rounded-lg flex items-center justify-center w-16 h-16">
              <img
                src={IMAGES.PLINKO}
                alt="Crypto King"
                loading="lazy"
                className="w-full h-full object-cover  rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[14px] font-normal leading-5 tracking-[−0.04em] font-[Anton]">
                Plinko
              </h4>
              <p className="text-[12px] text-white/80">
                Drop chips and watch them bounce to big rewards
              </p>
            </div>
            <button 
              onClick={handlePlinkoGame}
              className="bg-[#ED0CFF] text-white px-4 py-1.5 rounded-lg font-medium text-sm hover:opacity-90 transition-opacity"
            >
              Play
            </button>
          </div>
        </div>

        {/* ─── Quick Hustle Card ─── */}
        <div
          className="relative rounded-xl overflow-hidden bg-[#131313] cursor-pointer shadow-sm transform transition-all duration-300 hover:scale-[1.02]"
        >
          <div className="absolute inset-0 bg-black/40 border border-[#ED0CFF]  rounded-xl"></div>
          <div className="relative z-10 flex items-center gap-4 p-4">
            <div className="bg-[#29122b]  rounded-lg flex items-center justify-center w-16 h-16">
              <img
                src={IMAGES.QUICK_HUSTULE}
                alt="Crypto King"
                loading="lazy"
                className="w-full h-full object-cover  rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[14px] font-normal leading-5 tracking-wide font-[Anton]">
                Hi/LO
              </h4>
              <p className="text-[12px] text-white/80">
                Fast games, instant wins
              </p>
            </div>
            <button 
              onClick={handlePlayQuickHustle}
              className="bg-[#ED0CFF] text-white px-4 py-1.5 rounded-lg font-medium text-sm hover:opacity-90 transition-opacity"
            >
              Play
            </button>
          </div>
        </div>

        <div
          className="relative rounded-xl overflow-hidden bg-[#131313] cursor-pointer shadow-sm transform transition-all duration-300 hover:scale-[1.02]"
        >
          <div className="absolute inset-0 bg-black/40 border border-[#ED0CFF]  rounded-xl"></div>
          <div className="relative z-10 flex items-center gap-4 p-4">
            <div className="bg-white/10  rounded-lg flex items-center justify-center w-16 h-16">
              <img
                src="https://cdn.midjourney.com/415a32f8-f82e-4a92-b60c-267302e23f6b/0_2.png"
                alt="Crypto King"
                className="w-full h-full object-cover rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[14px] font-normal leading-5 tracking-[−0.04em] font-[Anton]">
                Street King
              </h4>
              <p className="text-[12px] text-white/80">
                Fast games, instant wins
              </p>
            </div>
            <button 
              onClick={handlePlayStreetHustle}
              className="bg-[#ED0CFF] text-white px-4 py-1.5 rounded-lg font-medium text-sm hover:opacity-90 transition-opacity"
            >
              Play
            </button>
          </div>
        </div>

        <div
          className="relative rounded-xl overflow-hidden bg-[#131313] cursor-pointer shadow-sm transform transition-all duration-300 hover:scale-[1.02]"
        >
          <div className="absolute inset-0 bg-black/40 border border-[#ED0CFF]  rounded-xl"></div>
          <div className="relative z-10 flex items-center gap-4 p-4">
            <div className="bg-white/10  rounded-lg flex items-center justify-center w-16 h-16">
              <img
                src="https://cdn.midjourney.com/415a32f8-f82e-4a92-b60c-267302e23f6b/0_3.png"
                alt="Crypto King"
                className="w-full h-full object-cover rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[14px] font-normal leading-5 tracking-[−0.04em] font-[Anton]">
                Street King 2
              </h4>
              <p className="text-[12px] text-white/80">
                Fast games, instant wins
              </p>
            </div>
            <button 
              onClick={handlePlayStreetHustle2}
              className="bg-[#ED0CFF] text-white px-4 py-1.5 rounded-lg font-medium text-sm hover:opacity-90 transition-opacity"
            >
              Play
            </button>
          </div>
        </div>
      </div>

      {/* Video Ad Modal */}
      {showVideoAd && (
        <Suspense fallback={<div>Loading...</div>}>
          <VideoAd
            onClose={() => setShowVideoAd(false)}
            onComplete={() => {
              setShowVideoAd(false);
            }}
          />
        </Suspense>
      )}

      {/* Boost Modal */}
      {showBoostModal && (
        <Suspense fallback={<div>Loading...</div>}>
          <BoostModal
            onClose={() => setShowBoostModal(false)}
            onWatchAd={() => {
              setShowBoostModal(false);
              setShowVideoAd(true);
            }}
            onShare={handleShare}
          />
        </Suspense>
      )}

      {/* Login Prompt Popup for unauthenticated actions */}
      <LoginPromptPopup
        isOpen={showLoginPrompt}
        onClose={() => setShowLoginPrompt(false)}
      />
    </div>
  );
};

/**
 * Export the Home component as default
 */
export default Home;
