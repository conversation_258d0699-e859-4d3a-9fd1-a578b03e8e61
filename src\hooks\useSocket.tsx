import { useEffect, useState, useRef, useCallback } from "react";
import { useAuth } from "../auth/AuthContext";

interface UseWebSocketOptions {
  connectOnMount?: boolean;
  autoReconnect?: boolean;
}

const useWebSocket = (url: string, options: UseWebSocketOptions = { connectOnMount: false, autoReconnect: false }) => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { accessToken } = useAuth();
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5; // Prevent infinite loops
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);




  // Connect to WebSocket
  const connectWebSocket = useCallback(() => {
    // Clear any existing reconnect timeouts
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Don't create a new connection if one already exists
    if (socket && (socket.readyState === WebSocket.CONNECTING || socket.readyState === WebSocket.OPEN)) {
      console.log("WebSocket connection already exists");
      return;
    }

    if (!url) {
      console.log("WebSocket URL is missing");
      return;
    }

    console.log("Connecting to WebSocket server...");
    const webSocket = new WebSocket(url);

    webSocket.onopen = () => {
      console.log("Connected to WebSocket server");
      setIsConnected(true);
      reconnectAttempts.current = 0; // Reset attempts on successful connection

      // Send authentication token
      const message = { access_token: accessToken };
      webSocket.send(JSON.stringify(message));
    };

    webSocket.onmessage = (event) => {
      // console.log("Message from server:", event);
    };

    webSocket.onerror = (error) => {
      console.error("WebSocket Error:", error);
    };

    webSocket.onclose = (event) => {
      console.log("Disconnected from WebSocket server", event.code, event.reason);
      setIsConnected(false);
      setSocket(null);

      // Attempt to reconnect only if autoReconnect is enabled
      if (options.autoReconnect && reconnectAttempts.current < maxReconnectAttempts) {
        reconnectAttempts.current += 1;
        console.log(`Reconnecting attempt ${reconnectAttempts.current}...`);

        reconnectTimeoutRef.current = setTimeout(() => {
          connectWebSocket();
        }, 3000);
      }
    };

    setSocket(webSocket);
  }, [url, accessToken, socket, options.autoReconnect]);

  // Disconnect from WebSocket
  const disconnectWebSocket = useCallback(() => {
    if (socket) {
      console.log("Manually disconnecting from WebSocket server");
      // Clear any pending reconnect attempts
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
        socket.close(1000, "User no longer betting"); // Normal closure
      }
      setSocket(null);
      setIsConnected(false);
    }
  }, [socket]);

  // Connect on mount if specified in options
  useEffect(() => {
    if (options.connectOnMount) {
      connectWebSocket();
    }

    // Cleanup on unmount
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }

      if (socket && (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING)) {
        socket.close(1000, "Component unmounting");
      }
    };
  }, [connectWebSocket, options.connectOnMount]);

  // Reconnect if token changes
  useEffect(() => {
    if (isConnected && socket) {
      // If token changes while connected, reconnect to update authentication
      socket.close(1000, "Authentication token changed");
      connectWebSocket();
    }
  }, [accessToken]);


  // Disconnect socket if accessToken is missing
  useEffect(() => {
    if (!accessToken ) {
      disconnectWebSocket()
    }
  }, [accessToken, disconnectWebSocket])

  return {
    socket,
    isConnected,
    connect: connectWebSocket,
    disconnect: disconnectWebSocket
  };
};

export default useWebSocket;