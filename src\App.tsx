import React, { useEffect, useState, Suspense } from 'react';
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Static imports for critical components
import Layout, { Balance } from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import { useAuth } from './auth/AuthContext';
import { GET_BALANCE_API } from './api/auth';
import { useSocketContext } from './context/socketProvider';
import { preloadCriticalResources, prefetchResources } from './utils/preloader';
import { lazyWithPreload } from './utils/lazyWithPreload';
import SportsbookPage from './pages/Sportsbook';
import { GameLoadingScreen } from './components/GameLoadingScreen/GameLoadingScreen';
import NativeAdService from './api/nativeAdService';

// Dynamic imports with lazyWithPreload
const Home = lazyWithPreload(() => import('./pages/Home'));
const Shop = lazyWithPreload(() => import('./pages/Shop'));
const Wallet = lazyWithPreload(() => import('./pages/Wallet'));
const Rewards = lazyWithPreload(() => import('./pages/Rewards'));
const Leaderboard = lazyWithPreload(() => import('./pages/Leaderboard'));
const Squads = lazyWithPreload(() => import('./pages/Squads'));
const Auth = lazyWithPreload(() => import('./pages/Auth'));
const Map = lazyWithPreload(() => import('./pages/Map'));
const Lottery = lazyWithPreload(() => import('./pages/Lottery'));

// Game components
const CrashGame = lazyWithPreload(() => import('./pages/CrashGame'));
const CrashGame2 = lazyWithPreload(() => import('./pages/CrashGame2'));
const CryptoKing = lazyWithPreload(() => import('./pages/CryptoKing'));
const HigherLowerGame = lazyWithPreload(
  () => import('./pages/HigherLowerGame')
);
const RollDiceGame = lazyWithPreload(() => import('./pages/RollDiceGame'));
const PlinkoGame = lazyWithPreload(() => import('./pages/PlinkoGame'));

// Modal components
const StoryIntro = lazyWithPreload(() => import('./components/StoryIntro'));
const ScratchCard = lazyWithPreload(() => import('./components/ScratchCard'));
const BoostModal = lazyWithPreload(() => import('./components/BoostModal'));
const DownloadAppModal = lazyWithPreload(
  () => import('./components/DownloadAppModal')
);
const WheelOfFortune = lazyWithPreload(
  () => import('./components/WheelOfFortune')
);
const TermsAndConditionsPage = lazyWithPreload(
  () => import('./components/TermsAndConditions')
);
const PrivacyPolicyPage = lazyWithPreload(
  () => import('./components/PrivacyPolicy')
);

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen bg-black">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
  </div>
);

function App() {
  const {
    connectSocket,
    isConnected,
    connectBalanceSocket,
    isBalanceConnected,
    balanceState,
  } = useSocketContext();
  const userBalance = Number(balanceState?.balance || 0);
  const { userProfile, loadUserProfile, isAuthenticated, login, accessToken } =
    useAuth();

  const [balance, setBalance] = useState<Balance>({
    coins: 0,
    fpp: 0,
    level: 0,
    xp: 0,
    real_money: '0',
  });
  const [showStoryIntro, setShowStoryIntro] = useState(false);
  const [showScratchCard, setShowScratchCard] = useState(false);
  const [showBoostModal, setShowBoostModal] = useState(false);
  const [showWheel, setShowWheel] = useState(false);
  const [showDownloadDialog, setShowDownloadDialog] = useState(false);
  const [actualBalance, setActualBalance] = useState(0);
  const [revealBalance, setRevealBalance] = useState(true);

  const fetchBalance = () =>
    fetch(GET_BALANCE_API, {
      headers: { Authorization: `Bearer ${accessToken}` },
    })
      .then((res) => {
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        return res.json();
      })
      .then(
        (
          data: {
            bonus_money: string;
            currency: string;
            id: string;
            real_money: string;
            updated_at: string;
            user_id: string;
          }[]
        ) => {
          const coins = Number(
            data.find((balance) => balance.currency === 'P')?.real_money
          );
          setBalance({ ...balance, coins });
        }
      )
      .catch((err) => console.error('Balance fetch failed:', err));

  useEffect(() => {
    if (revealBalance && userBalance) {
      setActualBalance(userBalance);
      setRevealBalance(false);
    }
  }, [userBalance, revealBalance]);

  useEffect(() => {
    if (!isAuthenticated || !accessToken) return;

    loadUserProfile();
    fetchBalance();
  }, [isAuthenticated, accessToken]);

  useEffect(() => {
    const hasSeenIntro = window.localStorage.getItem('hasSeenStoryIntro');

    if (!hasSeenIntro) {
      const timer = setTimeout(() => setShowStoryIntro(true), 100);
      return () => clearTimeout(timer);
    }
  }, []);

  useEffect(() => {
    if (!isConnected) {
      connectSocket();
    }
    if (!isBalanceConnected) {
      connectBalanceSocket();
    }

    // Preload critical resources
    preloadCriticalResources();

    // Prefetch non-critical resources
    prefetchResources();
  }, [isConnected]);

  const handleLogin = async (token: string) => {
    login(token);
    if (!window.nativeHandler) {
      setShowDownloadDialog(true);
    }
  };

  useEffect(() => {
    const hasShown = sessionStorage.getItem('downloadDialogShown');
    if (!window.nativeHandler && !hasShown) {
      setShowDownloadDialog(true);
      sessionStorage.setItem('downloadDialogShown', 'true');
    }
  }, []);

  // Listen for custom events
  React.useEffect(() => {
    const handleShowScratchCard = () => {
      setShowScratchCard(true);
    };

    const handleShowBoostModal = () => {
      setShowBoostModal(true);
    };

    const handleShowWheel = () => {
      setShowWheel(true);
    };

    window.addEventListener('show-scratch-card', handleShowScratchCard);
    window.addEventListener('show-boost-modal', handleShowBoostModal);
    window.addEventListener('show-wheel', handleShowWheel);

    return () => {
      window.removeEventListener('show-scratch-card', handleShowScratchCard);
      window.removeEventListener('show-boost-modal', handleShowBoostModal);
      window.removeEventListener('show-wheel', handleShowWheel);
    };
  }, []);

  const handleWin = (reward: { type: string; amount: number }) => {
    NativeAdService.showInterstitialAd();
    if (reward.type === 'fpp') {
      setBalance((prev) => ({
        ...prev,
        fpp: prev.fpp + Number(reward.amount),
      }));
    }
  };

  const handleShare = () => {
    setShowBoostModal(false);
    setBalance((prev) => ({
      ...prev,
      xp: Math.min(100, prev.xp + 10),
    }));
  };

  return (
    <>
      <Router>
        <Routes>
          <Route
            path="/auth"
            element={
              isAuthenticated ? (
                <Navigate to="/" replace />
              ) : (
                <Suspense fallback={<LoadingSpinner />}>
                  <Auth onLogin={handleLogin} />
                </Suspense>
              )
            }
          />

          <Route
            path="/"
            element={
              <>
                <Layout balance={actualBalance} />

                {userProfile && showStoryIntro && (
                  <div className="fixed inset-0 bg-black z-50">
                    <Suspense fallback={<LoadingSpinner />}>
                      <StoryIntro onClose={() => setShowStoryIntro(false)} />
                    </Suspense>
                  </div>
                )}

                {showScratchCard && (
                  <Suspense fallback={<GameLoadingScreen />}>
                    <ScratchCard
                      onClose={() => setShowScratchCard(false)}
                      onWin={handleWin}
                      balance={actualBalance}
                      setRevealBalance={setRevealBalance}
                    />
                  </Suspense>
                )}
                {showBoostModal && (
                  <Suspense fallback={<LoadingSpinner />}>
                    <BoostModal
                      onClose={() => setShowBoostModal(false)}
                      onWatchAd={() => {
                        setShowBoostModal(false);
                      }}
                      onShare={handleShare}
                    />
                  </Suspense>
                )}
                {showWheel && (
                  <Suspense fallback={<GameLoadingScreen />}>
                    <WheelOfFortune
                      onClose={() => setShowWheel(false)}
                      onWin={handleWin}
                      balance={actualBalance}
                      setRevealBalance={setRevealBalance}
                    />
                  </Suspense>
                )}

                <ToastContainer position="top-right" />
              </>
            }
          >
            <Route
              path="/terms-and-conditions"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <TermsAndConditionsPage />
                </Suspense>
              }
            />
            <Route
              path="/privacy-policy"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <PrivacyPolicyPage />
                </Suspense>
              }
            />
            <Route
              index
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <Home
                    setShowScratchCard={setShowScratchCard}
                    setShowWheel={setShowWheel}
                  />
                </Suspense>
              }
            />

            <Route
              path="shop"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <Shop balance={balance} onBalanceChange={setBalance} />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="wallet"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <Wallet />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="squads"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <Squads />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="rewards"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <Rewards />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="leaderboard"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <Leaderboard />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="map"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <Map />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="lottery"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <Lottery />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="street-king"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<GameLoadingScreen />}>
                    <CrashGame
                      onWin={handleWin}
                      balance={actualBalance}
                      setRevealBalance={setRevealBalance}
                    />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="street-king-2"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<GameLoadingScreen />}>
                    <CrashGame2
                      onWin={handleWin}
                      balance={actualBalance}
                      setRevealBalance={setRevealBalance}
                    />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="crypto-king"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<GameLoadingScreen />}>
                    <CryptoKing
                      onWin={handleWin}
                      balance={actualBalance}
                      setRevealBalance={setRevealBalance}
                    />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="quick-hustle"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<GameLoadingScreen />}>
                    <HigherLowerGame
                      onWin={handleWin}
                      balance={actualBalance}
                      setRevealBalance={setRevealBalance}
                    />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="roll-dice"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<GameLoadingScreen />}>
                    <RollDiceGame
                      onWin={handleWin}
                      balance={actualBalance}
                      setRevealBalance={setRevealBalance}
                    />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="plinko"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<GameLoadingScreen />}>
                    <PlinkoGame
                      onWin={handleWin}
                      balance={actualBalance}
                      setRevealBalance={setRevealBalance}
                    />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="sportsbook"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <SportsbookPage />
                  </Suspense>
                </ProtectedRoute>
              }
            />
          </Route>
        </Routes>
      </Router>
      <DownloadAppModal
        open={showDownloadDialog}
        onClose={() => setShowDownloadDialog(false)}
      />
    </>
  );
}

export default App;
