import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Layout from './components/Layout';

// Critical components - load immediately
import Auth from './pages/Auth';
import Home from './pages/Home';

// Non-critical - lazy load
const CrashGame = lazy(() => import('./pages/CrashGame'));
const CrashGame2 = lazy(() => import('./pages/CrashGame2'));
const CryptoKing = lazy(() => import('./pages/CryptoKing'));
const PlinkoGame = lazy(() => import('./pages/PlinkoGame'));
const RollDiceGame = lazy(() => import('./pages/RollDiceGame'));
const HigherLowerGame = lazy(() => import('./pages/HigherLowerGame'));
const Sportsbook = lazy(() => import('./pages/Sportsbook'));

const LoadingFallback = () => (
  <div className="flex items-center justify-center h-64">
    <div className="loading-spinner"></div>
  </div>
);

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/auth" element={<Auth />} />
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="crash-game" element={
            <Suspense fallback={<LoadingFallback />}>
              <CrashGame />
            </Suspense>
          } />
          <Route path="crash-game-2" element={
            <Suspense fallback={<LoadingFallback />}>
              <CrashGame2 />
            </Suspense>
          } />
          <Route path="crypto-king" element={
            <Suspense fallback={<LoadingFallback />}>
              <CryptoKing />
            </Suspense>
          } />
          <Route path="plinko" element={
            <Suspense fallback={<LoadingFallback />}>
              <PlinkoGame />
            </Suspense>
          } />
          <Route path="roll-dice" element={
            <Suspense fallback={<LoadingFallback />}>
              <RollDiceGame />
            </Suspense>
          } />
          <Route path="higher-lower" element={
            <Suspense fallback={<LoadingFallback />}>
              <HigherLowerGame />
            </Suspense>
          } />
          <Route path="sportsbook" element={
            <Suspense fallback={<LoadingFallback />}>
              <Sportsbook />
            </Suspense>
          } />
        </Route>
      </Routes>
      <Toaster position="top-center" />
    </Router>
  );
}

export default App;
